# 员工管理系统文档

## 概述

员工管理系统是MDV Dashboard的核心功能模块之一，用于管理公司所有员工的基本信息、职位分配、部门归属等关键数据。系统基于实际的组织结构图设计，支持完整的员工生命周期管理。

## 组织结构

### 部门架构

系统支持以下部门结构：

1. **设计团队 (Design Team)**
   - 负责所有设计事务
   - 包括法规解释、标准制定、日常设计工作
   - 销售团队沟通培训以及设计系统建立

2. **工程团队 (Engineering Team)**
   - 负责现场工程实施
   - 包括现场勘察、卸货管理、安装管理
   - 质量管理、政府检查和交接

3. **售后团队 (Aftersales Team)**
   - 负责售后服务工作
   - 包括维保和保修合同条款管理执行
   - 售后网点管理培训、配件销售和政府年检

4. **销售团队 (Sales Team)**
   - 负责业务拓展和客户管理
   - 包括商机管理、客户管理、订单管理
   - 应收账款闭环管理

5. **运营部 (Operation Department)**
   - 负责日常运营管理
   - 协调各部门工作

### 职位体系

#### 管理层
- **产品经理**: FLV产品经理，负责整体产品规划和管理

#### 设计团队
- **设计主管**: 团队负责人，统筹管理设计工作
- **设计师**: 执行具体设计任务，包括技术图纸绘制和方案制定

#### 工程团队
- **工程主管**: 工程团队负责人，统筹现场工程管理
- **技术员**: 执行现场工作，包括安装、调试、维护等

#### 售后团队
- **售后主管**: 售后团队负责人，负责各种资质工作
- **售后技术员**: 执行售后维护、故障排除等工作

#### 销售团队
- **销售主管**: 销售团队负责人，制定销售策略
- **销售支持**: 支持销售业务，负责特定区域销售
- **销售**: 一线销售人员，负责市场开发

#### 运营部
- **运营专员**: 负责日常运营管理工作

## 功能特性

### 1. 员工信息管理

#### 基本信息
- **员工编号**: 系统自动生成的唯一标识符
- **姓名**: 员工真实姓名
- **性别**: 男/女
- **联系方式**: 电话号码和邮箱地址

#### 职位信息
- **部门归属**: 员工所属部门
- **职位**: 员工在部门中的具体职位
- **入职日期**: 员工加入公司的日期
- **合同到期日期**: 劳动合同截止日期

#### 系统信息
- **最后登录**: 员工最近一次系统登录时间
- **密码状态**: 是否使用默认密码
- **备注**: 员工的职责描述和其他重要信息

### 2. 员工操作功能

#### 新增员工
- 录入完整的员工基本信息
- 分配部门和职位
- 设置入职日期和合同期限
- 添加联系方式和备注信息

#### 编辑员工
- 修改员工基本信息
- 调整部门和职位分配
- 更新联系方式
- 修改合同信息和备注

#### 删除员工
- 安全删除确认机制
- 防止误操作的二次确认
- 删除后数据无法恢复

#### 查看员工详情
- 详细的员工档案展示
- 分类显示各类信息
- 支持从详情页面直接编辑

### 3. 搜索和筛选

#### 搜索功能
- **姓名搜索**: 按员工姓名模糊搜索
- **部门搜索**: 按部门名称搜索
- **职位搜索**: 按职位名称搜索
- **联系方式搜索**: 按电话或邮箱搜索

#### 数据展示
- 分页显示，每页16条记录
- 响应式设计，支持移动端
- 表格形式展示关键信息
- 工具提示显示完整内容

### 4. 系统界面

#### 员工列表页面
- 清晰的表格布局
- 关键信息一目了然
- 操作按钮集中在右侧
- 支持分页浏览

#### 员工详情对话框
- 分组显示不同类型信息
- 美观的标签和图标设计
- 响应式布局适配各种屏幕
- 便捷的编辑入口

#### 新增/编辑对话框
- 表单验证确保数据完整性
- 下拉选择部门和职位
- 日期选择器便于操作
- 实时错误提示

## 技术实现

### 前端架构
- **框架**: Next.js 14 with App Router
- **UI组件**: Shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **状态管理**: React useState + useEffect
- **数据请求**: 基于fetch的API封装

### 组件结构
```
src/components/staff-management/
├── StaffManagementList.tsx      # 主列表组件
├── StaffManagementTable.tsx     # 表格展示组件
├── StaffManagementDialog.tsx    # 新增/编辑对话框
├── StaffDetailDialog.tsx        # 详情查看对话框
├── StaffManagementSearch.tsx    # 搜索组件
├── ConfirmDeleteDialog.tsx      # 删除确认对话框
└── index.ts                     # 组件导出
```

### 数据类型
```typescript
// 员工详情
interface StaffDetail {
  index: string              // 员工编号
  name: string              // 姓名
  gender: number            // 性别 (1:男, 2:女)
  department: Department    // 部门信息
  position: Position        // 职位信息
  join_date: string         // 入职日期
  contract_expiry_date: string // 合同到期日期
  phone: string             // 电话
  email: string             // 邮箱
  remark: string            // 备注
  last_login: string        // 最后登录
  is_default_password: boolean // 是否默认密码
}

// 部门信息
interface Department {
  id: number
  name: string
}

// 职位信息
interface Position {
  id: number
  name: string
}
```

### API接口

#### 获取员工列表
```
GET /staff/pagination-list
参数:
- page: 页码
- page_size: 每页大小
- search: 搜索关键词
```

#### 创建员工
```
POST /staff/create
参数: StaffOperation对象
```

#### 更新员工
```
PUT /staff/update/:index
参数: StaffOperation对象
```

#### 删除员工
```
DELETE /staff/delete
参数: { index: string }
```

#### 获取部门列表
```
GET /staff/department/list
参数: search (可选)
```

#### 获取职位列表
```
GET /staff/position/list
参数: search (可选)
```

## Mock数据

系统包含完整的Mock数据，基于真实的组织结构图设计：

### 数据特点
- **18名员工**: 覆盖所有部门和职位
- **真实姓名**: 使用组织结构图中的实际人员
- **完整信息**: 包含所有必需字段
- **分页支持**: 支持分页和搜索测试

### 数据分布
- 设计团队: 5人 (1名主管 + 4名设计师)
- 工程团队: 4人 (1名主管 + 3名技术员)
- 售后团队: 4人 (1名主管 + 3名技术员)
- 销售团队: 4人 (1名主管 + 1名支持 + 2名销售)
- 运营部: 1人 (运营专员)

## 路由配置

### 页面路由
- `/dashboard/staff` - 员工管理主页面
- `/dashboard/staff-logs` - 员工日志页面 (预留)

### 导航配置
在左侧导航栏的"员工管理"部分：
- 员工管理 - 跳转到员工列表页面
- 员工日志 - 跳转到日志页面

## 用户体验

### 交互设计
- **直观操作**: 所有操作按钮都有清晰的图标和文字说明
- **即时反馈**: 操作成功/失败都有Toast提示
- **防误操作**: 删除操作需要二次确认
- **响应式**: 支持桌面端和移动端访问

### 性能优化
- **分页加载**: 避免一次性加载大量数据
- **搜索防抖**: 300ms防抖延迟减少API调用
- **状态管理**: 合理的组件状态设计避免不必要的重渲染

### 错误处理
- **表单验证**: 前端实时验证用户输入
- **网络异常**: 优雅处理API请求失败
- **数据异常**: 空数据状态的友好提示

## 未来扩展

### 计划功能
1. **员工照片**: 支持头像上传和显示
2. **权限管理**: 基于角色的权限控制
3. **考勤管理**: 集成考勤打卡功能
4. **绩效评估**: 员工绩效管理模块
5. **组织图**: 可视化的组织结构图

### 技术改进
1. **数据缓存**: 实现更好的数据缓存策略
2. **批量操作**: 支持批量导入/导出
3. **实时更新**: WebSocket实时数据同步
4. **移动应用**: 开发专门的移动端应用

---

## 更新日志

### Version 1.0.0 (2024-01-15)
- ✅ 基础员工管理功能
- ✅ 员工详情查看功能
- ✅ 新增/编辑/删除员工
- ✅ 搜索和分页功能
- ✅ 基于组织结构图的Mock数据
- ✅ 响应式设计支持

---

*文档最后更新: 2024年1月15日* 