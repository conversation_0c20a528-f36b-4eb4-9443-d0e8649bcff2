# 3、警告信息

3.1不直接顯示，用於決定特定數據正常顯示還是顯示“ERR”；

3.2-3.5是針對用戶再Calculated Result部分的四個輸入進行的警告；

3.6是針對用戶輸入載重、根據轎廂面積計算的載重是否相符的警告；

3.7是針對用戶輸入載重對應的乘客人數、根據轎廂面積計算的標準乘客人數是否相符的警告；

3.8-3.14為警告欄內容，3.9-3.14中任意一條警告出現，都會導致3.8的警告欄通知。

## 3.1 後對重警告

若“不適用對重後置“與“對重後置貫通門“任意一個為True，則為True。

**3.1.1 不適用對重後置**

if ((Lift_Model in \['LTHX', 'LTHW', 'EVIN'\]) or (Lift_Model in \['EVIK', 'LTK'\] and Capacity > 1600) or (Lift_Model == "EVIK" and Standard == "GOST 33984.1" and Capacity == 630)) and CWT_Position == "REAR":

return True

else:

return False

**3.1.2 對重後置貫通門**

if CWT_Position == "REAR" and Through_Door == "Yes":

return True

else:

return False

## 3.2 針對用戶輸入Shaft Width 的警告

if Shaft_Width == "Enter":

return "" # 用戶尚未輸入

elif Shaft_Width < 最小井道寬度:

return "Wrong! to increase SW, adjust CW or Door"

elif Shaft_Width > 最小井道寬度 + 1000:

return "Separate beam is needed"

else:

return "" # 合理範圍，無需提示

## 3.3 針對用戶輸入Shaft Depth的警告

if Shaft_Depth == "Enter":

return "" # 用戶尚未輸入

elif Shaft_Depth < 最小井道深度:

return "Wrong! to increase SD, adjust CD"

elif CWT Position =="REAR" and Shaft_Depth > 最小井道深度 + 1000:

return "Separate beam may needed"

else:

return "" # 合理範圍，無需提示

## 3.4 針對用戶輸入Overhead的警告

if Overhead == "Enter":

return "" # 用戶尚未輸入

elif Overhead < 最小頂層高度:

return "Wrong! to increase K, reduce CH or V"

elif Lift_Model in \["LTHW", "LTW"\] and Overhead > 最小頂層高度 + 2000:

return "Beam to hang is needed at shaft top"

else:

return "" # 合理範圍，無需提示

## 3.5 針對用戶輸入Pit Depth的警告

if Pit_Depth == "Enter":

return "" # 用戶尚未輸入

elif Pit_Depth < 最小地坑深度:

return "Wrong! to increase pit or reduce speed"

elif Pit_Depth > 2500:

return "Need pit access door, please reduce if possible"

else:

return "" # 合理範圍，無需提示

## 3.6 載重警告

if Area_Cap > Capacity:

return "轎廂面積對應載重" + Area_Cap + "kg；"

Area Cap計算方法見M21

## 3.7 乘客人數警告

if (NPS查 < 載重計算乘客人數) and (Lift_Model not in \['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car'\]):

return "乘客人数" + 載重計算乘客人數 + "减小为" + NPS查 + "，建议增大轿厢尺寸！"

## 3.8 全局警告提示

3.9-3.14中出現任意一條錯誤警告，則提示“警 告：”。

## 3.9 轎廂深度警告

**3.9.1 計算相關參數值**

**3.9.1.1 井道前壁的門Y2F'**

Y2F' = Y2F + ST - 25

若其中有非合法值，則Y2F' = “-”。

**3.9.1.2 後壁的貫通門**

Y2B' = Y2B - ST + 25

若其中有非合法值，則Y2B' = “-”。

**3.9.1.3 前壁標準**

\-KTV - DD

**3.9.1.4 後壁標準**

if Through_Door == "Yes":

return Car_Depth - KTV + DD

else:

return "-"

**3.9.2 計算最小轎廂深度CDmin**

if Y2F' < 前壁標準:

a = 2 \* abs(-Y2F' + 前壁標準)

else:

a = 0

if Through_Door == "Yes" and Y2B' > 後壁標準:

b = 2 \* abs(Y2B' - 後壁標準)

else:

b = 0

if (Y2F' &lt; 前壁標準) or (Y2B' &gt; 後壁標準):

CDmin = max(a, b)向上取整到50的整數倍 + Car_Depth

else:

CDmin = Car_Depth

**3.9.3 進行比較**

if (Y2F' &lt; 前壁標準) or (Y2B' &gt; 後壁標準):

return "因对重框架限制，轿厢深度最小值 " + CDmin + "mm，请调整；"

此處若存在非合法值即有“-”顯示，實測不會報警。

## 3.10 載重警告

**3.10.1 計算允許面積超標最大載重**

if Car_Area_Exceed_the_Code == "Local allow"：

表5中查找大於等於“Capacity+1”的最小載重

else:

return Capacity

**3.10.2 判斷是否小於最小載重**

if (Lift_Model not in \['LTHX', 'LTHW', 'LTHX Car', 'LTHW Car'\]) and Area_Cap < 標準最小載重:

return True

else:

return False

其中**標準最小載重**為根據表1查到的當前電梯型號對應的最小載重。

**3.10.3 判斷是否超過最大載重**

if (Car_Area_Exceed_the_Code == "Local allow" and Area_Cap > 允許面積超標最大標準載重) or (Car_Area_Exceed_the_Code != "Local allow" and Area_Cap > 標準最大載重):

return True

else:

return False

其中**標準最大載重**為根據表1查到的當前電梯型號對應的最大載重。

**3.10.4 輸出載重警告**

result = ""

if (Car_Area_Exceed_the_Code == "Local allow" and (Area_Cap > 允許面積超標最大載重)) or (Car_Area_Exceed_the_Code != "Local allow" and (Area_Cap > Capacity)):

result += "轿厢面积超标，请减小轿厢尺寸！"

if 小於最小載重:

result += "建议加大轿厢尺寸，当前已小于标配载重最小值 " + 標準最小載重 + "kg；"

if 超過最大載重:

result += "建议减小轿厢尺寸，当前已超出标配载重最大值 " + 標準最大載重 + "kg；"

return result

## 3.11 轎廂大小警告

以下三項判斷

**3.11.1 轎廂寬度判斷**

if Car_Width < 轎廂寬度最小值:

return "请增大轿厢宽度！"

轎廂寬度最小值根據梯型查表獲得，見表6。

**3.11.2 轎廂深度判斷**

if Car_Depth < 轎廂深度最小值:

return "请增大轿厢深度！"

轎廂深度最小值根據梯型查表獲得，見表6

**3.11.3 開門高度判斷**

if Door_Height < 2000:

return "请增大开门高度！"

**3.11.4 輸出轎廂大小警告**

result = ""

if OFFSET(S8,0,AD8,1,1) == "-": # ？此處表格裡沒有值

result += "载重或速度超出标准配置表范围，请检查; "

result = result + 轎廂寬度判斷 + 轎廂深度判斷 + 開門高度判斷

door_limits = {

"C2": (600, 1300),

"S2": (600, 2000),

"C4": (1300, 3000),

"S3": (800, 2000)

}

\# 如果Door_Opening為以上四個之一且Door_Height不在範圍內：

result += "门宽请询售前"

return result

## 3.12 轎廂高度與開門高度差值警告

if Lift_Model in \['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car'\]:

if (Car_Height - Door_Height) < 100:

return "请增大轿厢高或减小开门高!"

elif (Car_Height - Door_Height) > 300:

return "请减小轿厢高或增大开门高!"

else:

return ""

else:

if (Car_Height - Door_Height) < 50:

return "请增大轿厢高或减小开门高!"

else:

return ""

## 3.13 運行高度警告

**3.13.1 獲取標準運行高度TH0**

見4.11。

**3.13.2 輸出警告數據**

if Travel_Height > TH0:

return "超出标配运行高度最大值 " + TH0 + "m；"

## 3.14 門寬+後對重警告

result = ""

if abs(De) > ((Car_Width - Door_Width) / 2):

result += "门宽超出轿厢范围，请减小； "

if 後對重警告:

result += "此梯型规格不适用后对重"

return result

# 5、查表不存在的值

EVIK、EVIN的额定载重可选择范围在：

\[320, 400, 450, 630, 800, 1000, 1150, 1250, 1350, 1600, 1800, 2000\]

标准配置中根据梯型和载重查表的范围为：

\[400, 630, 800, 1000, 1150, 1250, 1350, 1600\]

CWG：EVIK+SIDE+400、EVIK+300/450、EVIN+320/450/1800/2000

CSD2：EVIK+GOST+>1600，导致X1L计算出错->导致井道宽度计算出错

CWGB1：梯型为Car

CWe：EVIK+320/450、EVIN+320/450/1800/2000

ΔW、DS、FD：GOST+C4/S3/C6

CSD3：LTHX Car/LTHW Car+REAR