import math
from .FD_X1_table_query import query_FD_X1_value
from .CSD_table_query import query_CSD_value
from .CWe_table_query import query_CWe_value
from .delta_W_table_query import query_W_value
from .CWG_table_query import query_CWG_value
from .CWGB_table_query import query_CWGB_value
from .CW0_table_query import query_CW0_value
from .CD0_table_query import query_CD0_value
from .DO0_table_query import query_DO0_value
from .DW0_table_query import query_DW0_value
from .C0_table_query import query_C0_value
from .TH0_table_query import query_TH0_value
from .SW0_table_query import query_SW0_value
from .FD_table_query import query_FD_value
from .SD0_table_query import query_SD0_value
from .SH0_table_query import query_SH0_value
from .SP0_table_query import query_SP0_value
from .Capacity_query import get_min_max_capacity
from .Car_table_query import get_width_depth


# 测试数据
input_data = {
    "Lift_Model": "LTHX",
    "Capacity": 3000,
    "Speed": 10,
    "Travel_Height": 20,
    "Car_Width": 2200,
    "Car_Depth": 2500,
    "Car_Height": 2200,
    "CWT_Position": "SIDE",
    "CWT_Safety_Gear": "No",
    "Door_Opening": "C4",
    "Door_Width": 1800,
    "Door_Height": 2100,
    "Through_Door": "No",
    "Glass_Door": "No",
    "Standard": "EN81-1",
    "Door_Center_from_Car_Center": "Offset",
    "Car_Area_Exceed_the_Code": "Not allow",
    "Shaft_Tolerance": "Normal",
    "Marble_Floor": 30,
    "Shaft_Width": 3200,
    "Shaft_Depth": 3000,
    "Overhead": 4000,
    "Pit_Depth": 1500
}



Capacity_A_Person_table = [
    (20000, 33, 266, 31.42),
    (18000, 29.8, 240, 28.43),
    (15000, 25, 200, 23.83),
    (12000, 20.2, 160, 19.23),
    (10000, 17, 133, 16.125),
    (6000, 10.6, 80, 10.03),
    (5500, 9.8, 73, 9.225),
    (5000, 9, 66, 8.42),
    (4500, 8.2, 60, 7.73),
    (4000, 7.4, 53, 6.925),
    (3500, 6.6, 46, 6.12),
    (3000, 5.8, 40, 5.43),
    (2300, 4.68, 30, 4.28),
    (2000, 4.2, 26, 3.82),
    (1800, 3.88, 24, 3.59),
    (1600, 3.56, 21, 3.245),
    (1350, 3.1, 18, 2.85),
    (1250, 2.9, 16, 2.57),
    (1150, 2.7, 15, 2.43),
    (1000, 2.4, 13, 2.15),
    (800, 2, 10, 1.73),
    (630, 1.66, 8, 1.45),
    (450, 1.3, 6, 1.17),
    (400, 1.17, 5, 0.98),
    (320, 0.953, 4, 0.79)
]

# 解析表格数据并整理为字典
max_effective_area = [item[1] for item in Capacity_A_Person_table]
rated_cap = [item[0] for item in Capacity_A_Person_table]
person_count = [item[2] for item in Capacity_A_Person_table]
min_effective_area = [item[3] for item in Capacity_A_Person_table]

ST_table = {
        "Normal": {50: 15, 125: 25, float('inf'): 40},  # 对应 TH ≤ 50, TH ≤ 125, TH > 125
        "Well to do": {50: 40, 125: 60, float('inf'): 90}
    }

DS_table = {
    "GOST": {"C2": 95, "S2": 140},
    "non-GOST": {"C2": 90, "S2": 120, "C4": 120, "S3": 160, "C6": 160}
}

door_limits = {
    "C2": (600, 1300),
    "S2": (600, 2000),
    "C4": (1300, 3000),
    "S3": (800, 2000)
}

def roundup(x, base):
    return int(math.ceil(x / base)) * base

def get_standard(Lift_Model, CWT_Position, Capacity, Standard, Car_Width, Car_Depth, Door_Opening, \
                 Door_Width, Through_Door, Door_Center_from_Car_Center):
    # CW0 EVIK+SIDE+400/1150/1250/1350，EVIK/EVIN+320/450/1800/2000
    warning_text = []
    CW0 = query_CW0_value(Lift_Model, CWT_Position, Capacity, Standard)
    if CW0 == None:
        warn = True
        warning_text.append("梯型、对重位置、载重、标准组合异常")
        return False, warn, warning_text

    # CD0 EVIK+SIDE+400/1150/1250/1350，EVIK/EVIN+320/450/1800/2000
    CD0 = query_CD0_value(Lift_Model, CWT_Position, Capacity, Standard)
    if CD0 == None:
        warn = True
        warning_text.append("梯型、对重位置、载重、标准组合异常")
        return False, warn, warning_text
    
    # Door_Opening0 EVIK+SIDE+400. EVIK/EVIN+320/450/1800/2000
    if Lift_Model in ["LTHX Car", "LTHW Car"]:
        DO0 = "C4"
    else:
        DO0 = query_DO0_value(Lift_Model, CWT_Position, Capacity, Standard)
        if DO0 == None:
            warn = True
            warning_text.append("梯型、对重位置、载重、标准组合异常")
            return False, warn, warning_text

    #DW0 EVIK+SIDE+400. EVIK/EVIN+320/450/1800/2000
    DW0 = query_DW0_value(Lift_Model, CWT_Position, Capacity, Standard)
    if DW0 == None:
        warn = True
        warning_text.append("梯型、对重位置、载重、标准组合异常")
        return False, warn, warning_text

    # ThroughDoor
    if Lift_Model in ["LTHX", "LTHW", "EVIK", "EVIN", "LTK"]:
        TD0 = False
    else:
        TD0 = True

    # Center EVIK+SIDE+400. EVIK/EVIN+320/450/1800/2000
    if Lift_Model in ["LTHX Car", "LTHW Car"]:
        C0 = 0
    else:
        C0 = query_C0_value(Lift_Model, CWT_Position, Capacity, Standard)
        if C0 == None:
            warn = True
            warning_text.append("梯型、对重位置、载重、标准组合异常")
            return False, warn, warning_text
    
    if (Car_Width <= CW0) and (Car_Width - CW0 > -30) and (Car_Depth <= CD0) \
        and (Car_Depth - CD0 > -30) and (Door_Opening == DO0) and (Door_Width <= DW0) \
            and (Through_Door == TD0) and (Door_Center_from_Car_Center == C0):
        return True, False, ""
    else:
        return False, False, ""
    

def calculate(input_data):
    # 提取参数
    Lift_Model = input_data["Lift_Model"]
    Capacity = input_data["Capacity"]
    Speed = input_data["Speed"]
    Travel_Height = input_data["Travel_Height"]
    Car_Width = input_data["Car_Width"]
    Car_Depth = input_data["Car_Depth"]
    Car_Height = input_data["Car_Height"]
    CWT_Position = input_data["CWT_Position"]
    CWT_Safety_Gear = input_data["CWT_Safety_Gear"]
    Door_Opening = input_data["Door_Opening"]
    Door_Width = input_data["Door_Width"]
    Door_Height = input_data["Door_Height"]
    Through_Door = input_data["Through_Door"] == "YES"
    Glass_Door = input_data["Glass_Door"] == "YES"
    Standard = input_data["Standard"]
    Door_Center_from_Car_Center = input_data["Door_Center_from_Car_Center"]
    Car_Area_Exceed_the_Code = input_data["Car_Area_Exceed_the_Code"]
    Shaft_Tolerance = input_data["Shaft_Tolerance"]
    Marble_Floor = input_data["Marble_Floor"]
    Shaft_Width = input_data["Shaft_Width"]
    Shaft_Depth = input_data["Shaft_Depth"]
    Overhead = input_data["Overhead"]
    Pit_Depth = input_data["Pit_Depth"]

    warn = False
    warning_text = []

    # 输出定义
    output = {}
    output["Capacity"] = "ERR"
    output["Persons"] = "ERR"
    output["Shaft_Depth_min"] = "ERR"
    output["Shaft_Height_min"] = "ERR"
    output["Shaft_Pit_min"] = "ERR"
    output["Shaft_Width_min"] = "ERR"
    output["warn"] = False
    output["warning_text"] = []

    # 对重后置检查（输入错误警告）
    #不适用对重后置
    if ((Lift_Model in ["LTHX", "LTHX Car", "LTHW", "LTHW Car", "EVIN"]) or (Lift_Model in ["EVIK", "LTK"] and Capacity > 1600) \
        or (Lift_Model == "EVIK" and Standard == "GOST 33984.1" and Capacity == 630)) and CWT_Position == "REAR":
        back1 = True
    else:
        back1 = False

    # 对重后置贯通门
    if CWT_Position == "REAR" and Through_Door == "Yes":
        back2 = True
    else:
        back2 = False

    back = back1 or back2


    # 计算大理石地面额外增加的载重要求
    marble_cap = roundup((Car_Width * Car_Depth + Door_Width * 100) * Marble_Floor * 2500 / 1e9, 10)
    output["Marble_Cap"] = str(marble_cap) + "kg"
    

    # 计算轿厢面积对应载重Area Cap
    # 轿厢前壁宽度
    CFW = (Car_Width - Door_Width) / 2    

    # 是否开尽门
    if (Lift_Model not in ['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car']) or CFW > 0: 
        full_open = False
    else:
        full_open = True

    # FD_X1计算/查表
    FD_X1 = query_FD_X1_value(Standard, Door_Opening, full_open)
    
    # 计算轿厢面积
    A =  (Car_Width * Car_Depth +(2 if Through_Door else 1) * Door_Width * FD_X1) / 1000000

    # 计算载重
    # 1. 查找最大有效面积上界和下界
    max_area_up = min([area for area in max_effective_area if area >= A], default=None)
    max_area_down = max([area for area in max_effective_area if area < A], default=None)
    # 2. 查找对应的额定载重
    rated_cap_up = rated_cap[max_effective_area.index(max_area_up)]
    rated_cap_down = rated_cap[max_effective_area.index(max_area_down)]
    # 3. 使用线性插值计算载重Q
    if max_area_up == max_area_down:  # 如果上界和下界面积相等
        Q1 = rated_cap_up
    else:
        Q1 = rated_cap_down + (rated_cap_up - rated_cap_down) * (A - max_area_down) / (max_area_up - max_area_down)
    if Q1 > 2000:
        Q1 = roundup(Q1, 100)
    else:
        Q1 = roundup(Q1, 10)   
    if Lift_Model in ["LTHX Car", "LTHW Car"]:
        Q2 = roundup(200 * A, 100)
        Area_Cap = [Q2, Q1]
    Area_Cap = [Q1, Q1]
    output["Area_Cap_notCar"] = Area_Cap[1]
    output["Area_Cap"] = Area_Cap[0]

    # 对重后置警告
    if back:
        warn = True
        warning_text.append("此梯型规格不适用后对重")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    # Capacity
    output["Capacity"] = Capacity

    # 计算Persons
    # 计算国标乘客人数
    # 1. 查找最大有效面积上界和下界
    max_area_up = min([area for area in max_effective_area if area >= A], default=None)
    max_area_down = max([area for area in max_effective_area if area < A], default=None)
    # 1. 查找对应的乘客人数和最小有效面积
    person_count_up = person_count[max_effective_area.index(max_area_up)]
    person_count_down = person_count[max_effective_area.index(max_area_down)]
    min_area_up = min_effective_area[max_effective_area.index(max_area_up)]
    min_area_down = min_effective_area[max_effective_area.index(max_area_down)]
    # 2. 判断并进行插值计算
    if A < min_area_up:
        NPs = person_count_up - (person_count_up - person_count_down) * (min_area_up - A) / (min_area_up - min_area_down)
    else:
        NPs = person_count_up
    # 3. 向下取整到个位
    NPs = math.floor(NPs)

    # 计算载重乘客人数
    NPc = math.floor(Capacity / 75)

    # 汇总情况
    if (Lift_Model in ['LTHX', 'LTHW', 'LTHX Car', 'LTHW Car']) and Standard == "EN81-1":
        NP =  "-"
    else:
        NP =  min(NPs, NPc)
    output["Persons"] = NP


    # 计算最小井道宽度
    # 井道偏差ST
    if Shaft_Tolerance == "Critical":
        ST = 0
    else:
        if Travel_Height <= 50:
            ST = ST_table[Shaft_Tolerance][50]
        elif Travel_Height <= 125:
            ST = ST_table[Shaft_Tolerance][125]
        else:
            ST = ST_table[Shaft_Tolerance][float('inf')]

    # CWG查表，存在查不到值的情况（EVIK+SIDE+400、EVIK+320/450、EVIN+320/450/1800/2000为None）
    CWG = query_CWG_value(Lift_Model, Capacity, CWT_Position)
    if CWG == None:
        warn = True
        warning_text.append("梯型、载重、对重位置组合异常！")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    # 计算X1L和X1R
    CSD1 = query_CSD_value(Lift_Model, "CSD1", Capacity, Standard, Speed)
    if CSD1 == None:
        warn = True
        warning_text.append("梯型、载重、对重位置、速度组合异常！")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    # 可能会出None，EVIK+GOST+>1600,导致X1L计算出错，测试发现会导致Excel全面报错
    CSD2 = query_CSD_value(Lift_Model, "CSD2", Capacity, Standard, Speed)
    if CSD2 == None:
        warn = True
        warning_text.append("梯型、载重、对重位置、速度组合异常！")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    if CWT_Position == "REAR":    
        X1L = - Car_Width / 2 - CSD1 - ST
    else:
        X1L = - Car_Width / 2 - CSD2 - ST
    X1R = Car_Width / 2 + CSD1 + ST

    # 查询CWe，本身不会出空值，但与CWD高相关EVIK+320/450、EVIN+320/450/1800/2000时会查不到值
    CWe = query_CWe_value(Lift_Model, Capacity, Through_Door, CWT_Position)
    if CWe == None:
        warn = True
        warning_text.append("梯型、载重、贯通门、对重位置组合异常！")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    # 计算X2L和X2R，受CWG影响，EVIK+320/450会导致查表失败
    if CWT_Position == "REAR":
        # 查询CWGB1，梯型为Car的时候为None，导致计算问题
        CWGB1 = query_CWGB_value(Lift_Model, "CWGB1", Capacity)
        if CWGB1 == None:
            warn = True
            warning_text.append("梯型、载重、对重位置组合异常！")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
        
        X2L = - CWe - CWG / 2 - CWGB1 - ST
        X2R = - CWe + CWG / 2 + CWGB1 + ST

    # 计算X3L和X3R
    # GOST+C4/S3/C6会导致查不到值报错影响后续计算
    delta_W = query_W_value(Standard, Door_Opening, "W", Door_Width)
    if delta_W == None:
            warn = True
            warning_text.append("标准、开门类型组合异常")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    if Door_Opening == "C2":
        delta_W = delta_W + 50
    delta_WR = query_W_value(Standard, Door_Opening, "WR", Door_Width)
    if delta_WR == None:
            warn = True
            warning_text.append("标准、开门类型组合异常")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    if Door_Opening in ["S2", "S3"] and Glass_Door:
        delta_WR = delta_WR + 50
    # X3L0
    if Door_Opening in ['C2', 'S2']:
        X3L0 = -Door_Width - delta_W - ST
    elif Door_Opening == "C4":
        X3L0 = -0.75 * Door_Width - delta_W - ST
    elif Door_Opening == "S3":
        X3L0 = -5/6 * Door_Width - delta_W - ST
    elif Door_Opening == "C6":
        X3L0 = -2/3 * Door_Width - delta_W - ST
    else:
        X3L0 = None
    X3L0 = roundup(X3L0, 1)
    # X3R0
    if Door_Opening == "C2":
        X3R0 = Door_Width + delta_WR + ST
    elif Door_Opening in ['S2', 'S3']:
        X3R0 = 0.5 * Door_Width + delta_WR + ST
    elif Door_Opening == "C4":
        X3R0 = 0.75 * Door_Width + delta_WR + ST
    elif Door_Opening == "C6":
        X3R0 = 2/3 * Door_Width + delta_WR + ST
    else:
        X3R0 = None
    X3R0 = roundup(X3R0, 1)
    # Des
    if (min(X1L, X3L0) == X3L0 or max(X1R, X3R0) == X3R0) and \
        (X3R0 - X3L0) < (max(X3R0, X1R) - min(X3L0, X1L)):
        candidates = [X1L - X3L0, X1R - X3R0]
        # 选择绝对值最小的，保留符号
        Des = min(candidates, key=lambda x: abs(x))
    else:
        Des = 0
    # De0
    if Door_Center_from_Car_Center == "Offset":
        if Door_Opening in ['S2', 'S3']:
            De = abs(Car_Width - Door_Width) / 2
        else:
            if Des >= 0:
                symb = 1
            else:
                symb = -1
            De = symb * roundup(min(abs(Des), abs(Car_Width - Door_Width)/2), 25)
    else:
        De = Door_Center_from_Car_Center
    X3L = X3L0 + De
    X3R = X3R0 + De

    # 最终XL、XR
    if CWT_Position == "REAR":
        XL = min(X1L, X2L, X3L)
        XR = max(X1R, X2R, X3R)
    else:
        XL = min(X1L, X3L)
        XR = max(X1R, X3R)
    #SWc
    SWc =  roundup(XR - XL, 10)

    std, warn, text = get_standard(Lift_Model, CWT_Position, Capacity, Standard, Car_Width, Car_Depth, Door_Opening, \
                 Door_Width, Through_Door, Door_Center_from_Car_Center)
    if warn:
        warning_text.append(text)
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output
    #SW0 EVIK+SIDE+400,EVIK+SIDE+1150/1250/1350,EVIK+REAR+1600+GOST,LTHX Car/LTHW Car/,EVIK/EVIN+320/450/1800/2000
    SW0 = query_SW0_value(Lift_Model, CWT_Position, Capacity, Standard)
    if SW0 == None:
        warn = True
        warning_text.append("梯型、对重位置、载重、标准组合异常")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    #TH0 EVIK+2.0/2.5+400, EVIK +2.5+630, EVIN+2.0/2.5+400/630. EVIK/EVIN+320/450/1800/2000, LTK+2.0/2.5+630/2000
    if Lift_Model in ["LTHX", "LTHX Car", "LTHW", "LTHW Car"]:
        TH0 = 60
    else:
        TH0 = query_TH0_value(Lift_Model, Speed, Capacity)
        if TH0 == None:
            warn = True
            warning_text.append("梯型、速度、载重组合异常")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    if Shaft_Tolerance == "Critical":
        if std:
            if Travel_Height <= TH0:
                SW = SW0
            else:
                SW = max(SW0, SWc)
        else:
           SW = SWc
    else:
        SW = SWc
    output["Shaft_Width_min"] = SW


    # 计算最小井道深度
    # 获取基本参数
    # DS GOST+C4/S3/C6
    std_key = "GOST" if Standard == "GOST 33984.1" else "non-GOST"
    DS = DS_table.get(std_key, {}).get(Door_Opening, None)

    # FD GOST+C4/S3/C6
    # 轿厢前壁宽度
    CFW = (Car_Width - Door_Width) / 2    
    # 是否开尽门
    if (Lift_Model not in ['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car']) or CFW > 0: 
        full_open = False
    else:
        full_open = True
    FD = query_FD_value(Standard, Door_Opening, full_open)
    if FD == None:
        warn = True
        warning_text.append("梯型、标准、开门类型组合异常")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output

    # CSD3 LTHX/LTHX Car/LTHW/LTHW Car,EVIK+1800/2000 
    if CWT_Position == "REAR":
        CSD3 = query_CSD_value(Lift_Model, "CSD3", Capacity, Standard, Speed)
        if CSD3 == None:
            warn = True
            warning_text.append("梯型、载重、标准、速度组合异常")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    # CSD4
    CSD4 = query_CSD_value(Lift_Model, "CSD4", Capacity, Standard, Speed)
    if CSD4 == None:
        warn = True
        warning_text.append("梯型、载重、标准、速度组合异常")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output
    # KTVx
    KTVx = 100

    # DD
    if Door_Opening in ["S3", "C6"]:
        DD = 20
    else:
        DD = 40

    # CDSW GOST+C4/S3/C6
    CDSW = query_W_value(Standard, Door_Opening, "CDSW", Door_Width)
    if CDSW == None:
        warn = True
        warning_text.append("标准、开门类型、门宽组合异常")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output
    # CWGB2
    if CWT_Position == "SIDE":
        CWGB2 = query_CWGB_value(Lift_Model, "CWGB2", Capacity)
        if CWGB2 == None:
            warn = True
            warning_text.append("梯型、载重组合异常")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    else:
        CWGB2 = None

    # Y1F、Y1B
    # KTVs
    if Through_Door:
        KTVs = 0.5 * Car_Depth
    elif Lift_Model in ['LTHX', 'LTHW', 'LTHX Car', 'LTHW Car']:
        if Capacity <= 3000:
            a = 115
            if full_open:
                KTVs = 0.5 * Car_Depth - 115 - 40
            else:
                KTVs = 0.5 * Car_Depth - 115 - 80
        elif Capacity <= 5000:
            if full_open:
                KTVs = 0.5 * Car_Depth - 25 - 40
            else:
                KTVs = 0.5 * Car_Depth - 25 - 80
    elif Lift_Model == "LTK":
        KTVs = 0.5 * Car_Depth - 0.5 * CDSW - 52.5
    elif Lift_Model in ['EVIK', 'EVIN']:
        if Capacity < 630:
            KTVs = 0.5 * Car_Depth - 180
        else:
            KTVs = 0.5 * Car_Depth - 130

    # KTVM
    val = CWG / 2 + CWGB2 + 25 - CWe - DD - KTVs
    if (CWT_Position == "SIDE" and (CWe - CWG / 2 - CWGB2 - 25) < (-KTVs -  DD))\
          or (Through_Door == "Yes" and (CWe + CWG / 2 + CWGB2 + 25) > (Car_Depth - KTVs + DD)):
        if val < -KTVx:
            KTVM = -KTVx
        elif val > KTVx:
            KTVM = KTVx
        else:
            KTVM = val
    else:
        KTVM = 0

    # KTV
    KTV = KTVs + KTVM

    # Y1F、Y1B
    # 井道偏差ST
    if Shaft_Tolerance == "Critical":
        ST = 0
    else:
        if Travel_Height <= 50:
            ST = ST_table[Shaft_Tolerance][50]
        elif Travel_Height <= 125:
            ST = ST_table[Shaft_Tolerance][125]
        else:
            ST = ST_table[Shaft_Tolerance][float('inf')]
    Y1F = -KTV - DS - 30 - FD - ST
    # LTHX Car/LTHW Car + REAR将导致计算错误，源于CSD3
    if CWT_Position == "REAR":
        Y1B = Car_Depth - KTV + CSD3 + ST
    elif Through_Door == "Yes":
        Y1B = Car_Depth - KTV + DS + 30 + FD + ST
    else:
        Y1B = Car_Depth - KTV + CSD4 + ST

    #Y2F、Y2B
    if CWT_Position == "SIDE":
        Y2F = CWe - CWG / 2 - CWGB2 - ST
        if Through_Door:
            Y2B = CWe + CWG / 2 + CWGB2 + ST
        else: 
            Y2B = CWe + CWG / 2 + CWGB2 + 65 + ST

    #Y3F、Y3B
    if Lift_Model == "EVIN":
        MRD = 660
        # 此处Excel中有问题，Excel中判断一直为False
        ML = True
        Y3F = -MRD - ST
        Y3B = MRD + ST

    # 计算YF、YB
    if CWT_Position == "SIDE" and Lift_Model == "EVIN":
        YF = min(Y1F, Y2F, Y3F)
        YB = max(Y1B, Y2B, Y3B)
    elif CWT_Position == "SIDE" and Lift_Model != "EVIN":
        YF = min(Y1F, Y2F)
        YB = max(Y1B, Y2B)
    elif CWT_Position != "SIDE" and Lift_Model == "EVIN":
        YF = min(Y1F, Y3F)
        YB = max(Y1B, Y3B)
    else:
        YF = Y1F
        YB = Y1B

    # 计算SDc
    SDc = roundup(YB - YF, 10)
    
    # EVIK+SIDE+400/1150/1250/1350/1800/2000, EVIK+REAR+1350, EVIK+REAR+1600+GOST, EVIK+320/450
    if Lift_Model in ["LTHX Car", "LTHW Car"]:
        SD0 = None
    else:
        SD0 = query_SD0_value(Lift_Model, Capacity, CWT_Position, Door_Opening, Standard, Speed)
        if SD0 == None:
            warn = True
            warning_text.append("梯型、载重、对重位置、开门类型、标准、速度组合异常！")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    if Shaft_Tolerance == "Critical":
        if std:
            if Travel_Height <= TH0:
                SD = SD0
            else:
                SD = max(SD0, SDc)
        else:
           SD = SDc
    else:
        SD = SDc

    output["Shaft_Depth_min"] = SD

   
   # 计算最小顶层高度
   # SH0 EVIK/EVIN+320/450/1800/2000,EVIK+GOST+1.6+1150/1250/1350,EVIK+2.0+400,EVIK+2.5+400/600,EVIN+2.0/2.5+400/630+nonGOST,EVIN+2.0/2.5+800/1000+GOST,EVIN+1150/1250/1350+1.6+GOST,EVIN+1600+GOST
    SH0 = query_SH0_value(Lift_Model, Capacity, CWT_Position, Car_Depth, Standard, Speed)
    if SH0 == None:
        warn = True
        warning_text.append("梯型、载重、对重位置、轿厢宽度、标准、速度组合异常！")
        output["warn"] = warn
        output["warning_text"] = warning_text
        return output
    #CH0
    if Lift_Model in ["LTHX", "LTHX Car", "LTHW", "LTHW Car"]:
        CH0 = 2200
    elif Lift_Model in ["EVIK", "EVIN"]:
        if Standard == "GOST 33984.1":
            CH0 = 2300
        else:
            CH0 = 2100
    else:
        CH0 = 2500

    # SH 
    if Shaft_Tolerance == "Well to do":
        SH = SH0 + Car_Height - CH0 + 100
    else:
        SH = SH0 + Car_Height - CH0
    output["Shaft_Height_min"] = SH

    # 计算最小地坑深度
    if Shaft_Tolerance == "Well to do":
        SP = query_SP0_value(Lift_Model, Capacity, Standard, Speed) + 100
        if SP == None:
            warn = True
            warning_text.append("梯型、载重、标准、速度组合异常！")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    else:
        SP = query_SP0_value(Lift_Model, Capacity, Standard, Speed)
        if SP == None:
            warn = True
            warning_text.append("梯型、载重、标准、速度组合异常！")
            output["warn"] = warn
            output["warning_text"] = warning_text
            return output
    output["Shaft_Pit_min"] = SP


    # Shaft Width警告
    if Shaft_Width < SW:
        warn = True
        warning_text.append("Wrong! to increase SW, adjust CW or Door")
    elif Shaft_Width > SW + 1000:
        warn = True
        warning_text.append("Separate beam is needed")

    # Shaft Depth的警告
    if Shaft_Depth < SD:
        warn = True
        warning_text.append("Wrong! to increase SD, adjust CD")
    elif CWT_Position == "REAR" and Shaft_Width > SD + 1000:
        warn = True
        warning_text.append("Separate beam may needed")
    
    # Overhead的警告，梯型判断有问题
    if Overhead < SH:
        warn = True
        warning_text.append("Wrong! to increase K, reduce CH or V")
    elif Lift_Model in ["LTHW", "LTW"] and Overhead > SH + 2000:
        warn = True
        warning_text.append("Beam to hang is needed at shaft top")

    # Pit Depth的警告
    if Pit_Depth < SP:
        warn = True
        warning_text.append("Wrong! to increase pit or reduce speed")
    elif Pit_Depth > 2500:
        warn = True
        warning_text.append("Need pit access door, please reduce if possible")

    # 载重警告
    if Area_Cap[0] > Capacity:
        warn = True
        warning_text.append(f"轿厢面积对应载重{Area_Cap[0]}kg")
    
    # 乘客人数警告
    if (NPs < NPc) and (Lift_Model not in ["LTHX", "LTHX Car", "LTHW", "LTHW Car"]):
        warn = True
        warning_text.append(f"乘客人数{NPs}减小为{NPc},建议增大轿厢尺寸！")

    # 全局警告部分
    # 轿厢深度警告
    if CWT_Position == "SIDE":
        if (Y2F + ST - 25) < (- KTV - DD):
            F_warn = True
            a = 2 * abs(- Y2F - ST + 25 - KTV - DD)
        else:
            F_warn = False
            a = 0
        if Through_Door and ((Y2F - ST + 25) > (Car_Depth - KTV + DD)):
            B_warn = True
            b = 2 * abs(Y2B - ST + 25 - Car_Depth + KTV - DD)
        else: 
            B_warn = False
            b = 0
        if F_warn or B_warn:
            CDmin = roundup(max(a, b), 50) + Car_Depth
            warn = True
            warning_text.append(f"因对重框架限制，轿厢深度最小值{CDmin}mm,请调整")

    # 载重警告
    if Car_Area_Exceed_the_Code == "Local allow":
        Cap = min([cap for cap in rated_cap if cap >= (Capacity + 1)], default=None)
    else:
        Cap = Capacity
    if Area_Cap[0] > Cap:
        warn = True
        warning_text.append("轿厢面积超标，请减小轿厢尺寸！")
    capacity_min, capacity_max = get_min_max_capacity(Lift_Model)
    if (Lift_Model not in ['LTHX', 'LTHW', 'LTHX Car', 'LTHW Car']) and Area_Cap[0] < capacity_min:
        warn = True
        warning_text.append(f"建议加大轿厢尺寸，当前已小于标配载重最小值{capacity_min}kg")
    if (Car_Area_Exceed_the_Code == "Local allow" and Area_Cap[0] > Cap)\
          or (Car_Area_Exceed_the_Code != "Local allow" and Area_Cap[0] > capacity_max):
        warn = True
        warning_text.append(f"建议减小轿厢尺寸，当前已超出标配载重最大值{capacity_max}kg")

    # 轿厢大小警告
    Car_Width_min = get_width_depth(Lift_Model, Capacity, "min_width")
    Car_Depth_min = get_width_depth(Lift_Model, Capacity, "min_depth")
    if Car_Width < Car_Width_min:
        warn = True
        warning_text.append("请增大轿厢宽度！")
    if Car_Depth < Car_Depth_min:
        warn = True
        warning_text.append("请增大轿厢深度！")
    if Door_Height < 2000:
        warn = True
        warning_text.append("请增大开门高度！")
    if Door_Opening in door_limits:
        min_height, max_height = door_limits[Door_Opening]
        if not (min_height <= Door_Width <= max_height):
            warn = True
            warning_text.append("门宽请询售前")

    # 轿厢高度与开门高度差值警告
    if Lift_Model in ['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car']:
        if (Car_Height - Door_Height) < 100:
            warn = True
            warning_text.append("请增大轿厢高或减小开门高!")
        elif (Car_Height - Door_Height) > 300:
            warn = True
            warning_text.append("请减小轿厢高或增大开门高!")
    else:
        if (Car_Height - Door_Height) < 50:
            warn = True
            warning_text.append("请增大轿厢高或减小开门高!")

    # 运行高度警告
    if Travel_Height > TH0:
        warn = True
        warning_text.append(f"超出标配运行高度最大值{TH0}m")

    # 门宽警告
    if abs(De) > ((Car_Width - Door_Width) / 2):
        warn = True
        warning_text.append("门宽超出轿厢范围，请减小")


    output["warn"] = warn
    output["warning_text"] = warning_text
    return output


if __name__ == "__main__":
    output = calculate(input_data)
    for key, value in output.items():
        print(f"{key}:{value}")