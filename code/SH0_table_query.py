lookup_rules_SH0 = {
    "R1": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 3800),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3500)
    ],
    "R2": [
        (lambda cwt, std, cd: cwt == "REAR" and cd >= 1565, 4220),
        (lambda cwt, std, cd: cwt != "REAR" or cd < 1565, 4100)
    ],
    "R3": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4050),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3700)
    ],
    "R4": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4000),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3700)
    ],
    "R5": [
        (lambda cwt, std, cd: cwt == "REAR" and cd >= 1565, 4320),
        (lambda cwt, std, cd: cwt != "REAR" or cd < 1565, 4200)
    ],
    "R6": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4250),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3850)
    ],
    "R7": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4100),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3750)
    ],
    "R8": [
        (lambda cwt, std, cd: cwt == "REAR" and cd >= 1565, 4420),
        (lambda cwt, std, cd: cwt != "REAR" or cd < 1565, 4300)
    ],
    "R9": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4300),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3950)
    ],
    "R10": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4150),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3850)
    ],
    "R11": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4250),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3850)
    ],
    "R12": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4400),
        (lambda cwt, std, cd: std == "GOST 33984.1", 4050)
    ],
    "R13": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4450),
        (lambda cwt, std, cd: std == "GOST 33984.1", 4100)
    ],
    "R14": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4600),
        (lambda cwt, std, cd: std == "GOST 33984.1", 4100)
    ],
    "R15": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4800),
        (lambda cwt, std, cd: std == "GOST 33984.1", 4250)
    ],
    "R16": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 3800),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3450)
    ],
    "R17": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4050),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R18": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4000),
        (lambda cwt, std, cd: std == "GOST 33984.1", 3650)
    ],
    "R19": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4250),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R20": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4300),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R21": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4150),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R22": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4250),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R23": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4400),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R24": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4450),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R25": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4600),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ],
    "R26": [
        (lambda cwt, std, cd: std != "GOST 33984.1", 4800),
        (lambda cwt, std, cd: std == "GOST 33984.1", None)
    ]
}

SH0_table = {
    "LTHX": {
        0.50: {1600: 3950, 2000: 3950, 3000: 3950, 4000: 3950, 5000: 3950},
        1.00: {1600: 3950, 2000: 3950, 3000: 3950, 4000: 3950, 5000: 3950}
    },
    "LTHX Car": {
        0.50: {3000: 4700, 4000: 4700, 5000: 4700},
        1.00: {3000: 4700, 4000: 4700, 5000: 4700}
    },
    "LTHW": {
        0.50: {1600: 4300, 2000: 4300, 3000: 4650, 4000: 5000, 5000: 5000},
        1.00: {1600: 4300, 2000: 4300, 3000: 4650, 4000: 5000, 5000: 5000}
    },
    "LTHW Car": {
        0.50: {3000: 5100, 4000: 5300, 5000: 5300},
        1.00: {3000: 5100, 4000: 5300, 5000: 5300}
    },
    "EVIK": {
        1.00: {400: "R1", 630: "R1", 800: "R1", 1000: "R1", 1150: "R2", 1250: "R2", 1350: "R2", 1600: "R3"},
        1.50: {400: "R4", 630: "R4", 800: "R4", 1000: "R4", 1150: "R5", 1250: "R5", 1350: "R5", 1600: "R6"},
        1.60: {400: "R4", 630: "R4", 800: "R4", 1000: "R4", 1150: None, 1250: None, 1350: None, 1600: "R6"},
        1.75: {400: "R7", 630: "R7", 800: "R7", 1000: "R7", 1150: "R8", 1250: "R8", 1350: "R8", 1600: "R9"},
        2.00: {630: "R10", 800: "R10", 1000: "R11", 1150: 4500, 1250: 4500, 1350: 4500, 1600: "R12"},
        2.50: {800: "R13", 1000: "R14", 1150: 4750, 1250: 4750, 1350: 4750, 1600: "R15"}
    },
    "EVIN": {
        1.00: {400: "R16", 630: "R16", 800: "R16", 1000: "R16", 1150: "R2", 1250: "R2", 1350: "R2", 1600: "R17"},
        1.50: {400: "R18", 630: "R18", 800: "R18", 1000: "R18", 1150: "R5", 1250: "R5", 1350: "R5", 1600: "R19"},
        1.60: {400: "R18", 630: "R18", 800: "R18", 1000: "R18", 1150: None, 1250: None, 1350: None, 1600: "R19"},
        1.75: {400: "R7", 630: "R7", 800: "R7", 1000: "R7", 1150: "R8", 1250: "R8", 1350: "R8", 1600: "R20"},
        2.00: {800: "R21", 1000: "R22", 1150: 4500, 1250: 4500, 1350: 4500, 1600: "R23"},
        2.50: {800: "R24", 1000: "R25", 1150: 4750, 1250: 4750, 1350: 4750, 1600: "R26"}
    },
    "LTK": {
        1.00: {630: "R2", 800: "R2", 1000: "R2", 1150: "R2", 1250: "R2", 1350: "R2", 1600: "R2", 2000: "R2"},
        1.50: {630: "R5", 800: "R5", 1000: "R5", 1150: "R5", 1250: "R5", 1350: "R5", 1600: "R5", 2000: "R5"},
        1.75: {630: "R8", 800: "R8", 1000: "R8", 1150: "R8", 1250: "R8", 1350: "R8", 1600: "R8", 2000: "R8"},
        2.00: {800: 4500, 1000: 4500, 1150: 4500, 1250: 4500, 1350: 4500, 1600: 4500},
        2.50: {800: 4750, 1000: 4750, 1150: 4750, 1250: 4750, 1350: 4750, 1600: 4750}
    }
    }

def query_SH0_value(Lift_Model, Capacity, CWT_Position, Car_Depth, Standard, Speed):
    key = SH0_table.get(Lift_Model, {}).get(Speed, {}).get(Capacity)
    if isinstance(key, str):
        for cond, val in lookup_rules_SH0.get(key, []):
            if cond(CWT_Position, Standard, Car_Depth):
                return val
    else:
        return key