TH0_table = {
    "EVIK": {
        1.00: {400: 50, 630: 50, 800: 50, 1000: 50, 1150: 50, 1250: 50, 1350: 50, 1600: 50},
        1.50: {400: 75, 630: 75, 800: 75, 1000: 75, 1150: 75, 1250: 75, 1350: 75, 1600: 75},
        1.60: {400: 75, 630: 75, 800: 75, 1000: 75, 1150: 75, 1250: 75, 1350: 75, 1600: 75},
        1.75: {400: 90, 630: 90, 800: 90, 1000: 90, 1150: 90, 1250: 90, 1350: 90, 1600: 90},
        2.00: {630: 110, 800: 110, 1000: 110, 1150: 110, 1250: 110, 1350: 110, 1600: 110},
        2.50: {800: 125, 1000: 125, 1150: 125, 1250: 125, 1350: 125, 1600: 125}
    },
    "EVIN": {
        1.00: {400: 50, 630: 50, 800: 50, 1000: 50, 1150: 50, 1250: 50, 1350: 50, 1600: 50},
        1.50: {400: 75, 630: 75, 800: 75, 1000: 75, 1150: 75, 1250: 75, 1350: 75, 1600: 75},
        1.60: {400: 75, 630: 75, 800: 75, 1000: 75, 1150: 75, 1250: 75, 1350: 75, 1600: 75},
        1.75: {400: 90, 630: 90, 800: 90, 1000: 90, 1150: 90, 1250: 90, 1350: 90, 1600: 90},
        2.00: {800: 110, 1000: 110, 1150: 110, 1250: 110, 1350: 110, 1600: 110},
        2.50: {800: 125, 1000: 125, 1150: 125, 1250: 125, 1350: 125, 1600: 125}
    },
    "LTK": {
        1.00: {630: 50, 800: 50, 1000: 50, 1150: 50, 1250: 50, 1350: 50, 1600: 50, 2000: 50},
        1.50: {630: 75, 800: 75, 1000: 75, 1150: 75, 1250: 75, 1350: 75, 1600: 75, 2000: 75},
        1.75: {630: 90, 800: 90, 1000: 90, 1150: 90, 1250: 90, 1350: 90, 1600: 90, 2000: 90},
        2.00: {800: 110, 1000: 110, 1150: 110, 1250: 110, 1350: 110, 1600: 110},
        2.50: {800: 125, 1000: 125, 1150: 125, 1250: 125, 1350: 125, 1600: 125}
    }
}

def query_TH0_value(Lift_Model, Speed, Capacity):
    table = TH0_table.get(Lift_Model).get(Speed)
    return table.get(Capacity, None)