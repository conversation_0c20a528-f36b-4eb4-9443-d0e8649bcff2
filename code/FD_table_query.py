FD_table = {
    "GOST": {
        "C2": {False: 155, True: 75},
        "S2": {False: 140, True: 90}
    },
    "non-GOST": {
        "C2": {False: 140, True: 60},
        "S2": {False: 176, True: 96},
        "C4": {False: 176, True: 96},
        "S3": {False: 212, True: 132},
        "C6": {False: 212, True: 132}
    }
}

def query_FD_value(Standard, Door_Opening, full_open):
    std_key = "GOST" if Standard == "GOST 33984.1" else "non-GOST"
    entry = FD_table.get(std_key, {}).get(Door_Opening)
    if not entry:
        return None
    else:
        return entry.get(full_open)