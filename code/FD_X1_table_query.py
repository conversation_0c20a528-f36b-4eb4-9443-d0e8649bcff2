FD_X1_table = {
    "C2": {False: 85, True: 5},
    "S2": {False: 104, True: 24},
    "C4": {False: 104, True: 24},
    "S3": {False: 139, True: 59},
    "C6": {False: 139, True: 59}
}

def query_FD_X1_value(Standard, Door_Opening, full_open):
    if (Standard == "EN81-20" and Door_Opening == "C2") or (Standard == "GOST 33984.1"):
        return 0
    else:
        return FD_X1_table.get(Door_Opening, {}).get(full_open, None)
