lookup_rules_SP0 = {
    "R1": [
        (lambda std: std != "GOST 33984.1", 1150),
        (lambda std: std == "GOST 33984.1", None)
    ],
    "R2": [
        (lambda std: std != "GOST 33984.1", 1350),
        (lambda std: std == "GOST 33984.1", None)
    ],
    "R3": [
        (lambda std: std != "GOST 33984.1", 1400),
        (lambda std: std == "GOST 33984.1", None)
    ],
    "R4": [
        (lambda std: std != "GOST 33984.1", 1450),
        (lambda std: std == "GOST 33984.1", None)
    ],
    "R5": [
        (lambda std: std != "GOST 33984.1", 1650),
        (lambda std: std == "GOST 33984.1", None)
    ]
}

SP0_table = {
    "LTHX": {
        0.50: {1600: 1350, 2000: 1350, 3000: 1400, 4000: 1500, 5000: 1500},
        1.00: {1600: 1350, 2000: 1350, 3000: 1400, 4000: 1500, 5000: 1500}
    },
    "LTHX Car": {
        0.50: {3000: 1500, 4000: 1800, 5000: 1800},
        1.00: {3000: 1500, 4000: 1800, 5000: 1800}
    },
    "LTHW": {
        0.50: {1600: 1350, 2000: 1350, 3000: 1400, 4000: 1550, 5000: 1550},
        1.00: {1600: 1350, 2000: 1350, 3000: 1400, 4000: 1550, 5000: 1550}
    },
    "LTHW Car": {
        0.50: {3000: 1500, 4000: 1800, 5000: 1800},
        1.00: {3000: 1500, 4000: 1800, 5000: 1800}
    },
    "EVIK": {
        1.00: {400: 1100, 630: 1100, 800: 1100, 1000:1100, 1150: 1250, 1250: 1250, 1350: 1250, 1600: 1150},
        1.50: {400: 1300, 630: 1300, 800: 1300, 1000: 1300, 1150: 1350, 1250: 1350, 1350: 1350, 1600: 1350},
        1.60: {400: 1300, 630: 1300, 800: 1300, 1000: 1300, 1150: None, 1250: None, 1350: None, 1600: 1350},
        1.75: {400: 1350, 630: 1350, 800: 1350, 1000: 1350, 1150: 1400, 1250: 1400, 1350: 1400, 1600: 1400},
        2.00: {630: 1400, 800: 1400, 1000: 1400, 1150: 1450, 1250: 1450, 1350: 1450, 1600: 1450},
        2.50: {800: 1650, 1000: 1650, 1150: 1600, 1250: 1600, 1350: 1600, 1600: 1700}
    },
    "EVIN": {
        1.00: {400: 1100, 630: 1100, 800: 1100, 1000: 1100, 1150: 1250, 1250:1250, 1350: 1250, 1600: "R1"},
        1.50: {400: 1300, 630: 1300, 800: 1300, 1000: 1300, 1150: 1350, 1250: 1350, 1350: 1350, 1600: "R2"},
        1.60: {400: 1300, 630: 1300, 800: 1300, 1000: 1300, 1150: None, 1250: None, 1350: None, 1600: "R2"},
        1.75: {400: 1350, 630: 1350, 800: 1350, 1000: 1350, 1150: 1400, 1250: 1400, 1350: 1400, 1600: "R3"},
        2.00: {800: "R3", 1000: "R3", 1150: 1450, 1250: 1450, 1350: 1450, 1600: "R4"},
        2.50: {800: "R5", 1000: "R5", 1150: 1600, 1250: 1600, 1350: 1600, 1600: "R5"}
    },
    "LTK": {
        1.00: {630: 1250, 800: 1250, 1000: 1250, 1150: 1250, 1250: 1250, 1350: 1250, 1600: 1250, 2000: 1250},
        1.50: {630: 1350, 800: 1350, 1000: 1350, 1150: 1350, 1250: 1350, 1350: 1350, 1600: 1350, 2000: 1350},
        1.75: {630: 1400, 800: 1400, 1000: 1400, 1150: 1400, 1250: 1400, 1350: 1400, 1600: 1400, 2000: 1400},
        2.00: {800: 1450, 1000: 1450, 1150: 1450, 1250: 1450, 1350: 1450, 1600: 1450},
        2.50: {800: 1600, 1000: 1600, 1150: 1600, 1250: 1600, 1350: 1600, 1600: 1600}
    }
    }

def query_SP0_value(Lift_Model, Capacity, Standard, Speed):
    key = SP0_table.get(Lift_Model, {}).get(Speed, {}).get(Capacity)
    if isinstance(key, str):
        for cond, val in lookup_rules_SP0.get(key, []):
            if cond(Standard):
                return val
    else:
        return key