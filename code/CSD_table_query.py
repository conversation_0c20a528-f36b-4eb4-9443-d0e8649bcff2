

# 参数条件规则定义
lookup_rules_CSD = {
    "a": [
        (lambda cap, std, spd: cap <= 2000, 260),
        (lambda cap, std, spd: cap <= 3000, 300),
        (lambda cap, std, spd: cap <= 5000, 370),
        (lambda cap, std, spd: cap > 5000, None),
    ],
    "b": [
        (lambda cap, std, spd: cap <= 2000, 585),
        (lambda cap, std, spd: cap <= 3000, 625),
        (lambda cap, std, spd: cap <= 5000, 740),
        (lambda cap, std, spd: cap > 5000, None),
    ],
    "c": [
        (lambda cap, std, spd: cap <= 2000, 300),
        (lambda cap, std, spd: cap <= 3000, 320),
        (lambda cap, std, spd: cap <= 5000, 450),
        (lambda cap, std, spd: cap > 5000, None),
    ],
    "d": [
        (lambda cap, std, spd: cap <= 2000, 750),
        (lambda cap, std, spd: cap <= 3000, 880),
        (lambda cap, std, spd: cap <= 5000, 1000),
        (lambda cap, std, spd: cap > 5000, None),
    ],
    "e": [
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1050, 170),
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1600, 190),
        (lambda cap, std, spd: std == "GOST 33984.1" and cap > 1600, 205),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1600, 190),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 2000, 205),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap > 2000, None),
    ],
    "f": [
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1050, 380),
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1600, 460),
        (lambda cap, std, spd: std == "GOST 33984.1" and cap > 1600, None),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1050, 410),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1250, 460),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1600, 490),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 2000, 555),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap > 2000, None),
    ],
    "g": [
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1050, 290),
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1600, 350),
        (lambda cap, std, spd: std == "GOST 33984.1" and cap > 1600, None),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1050, 290),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1600, 390),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap > 1600, None),
    ],
    "h": [
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1050, 170),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1050, 215),
        (lambda cap, std, spd: cap <= 1600, 215),
        (lambda cap, std, spd: cap <= 2000, 230),
        (lambda cap, std, spd: cap > 2000, None),
    ],
    "i": [
        (lambda cap, std, spd: std == "GOST 33984.1" and cap <= 1050, 380),
        (lambda cap, std, spd: std != "GOST 33984.1" and cap <= 1050, 410),
        (lambda cap, std, spd: cap <= 1150, 460),
        (lambda cap, std, spd: cap <= 1600, 490),
        (lambda cap, std, spd: cap <= 2000, 555),
        (lambda cap, std, spd: cap > 2000, None),
    ],
    "j": [
        (lambda cap, std, spd: cap <= 1600, 240),
        (lambda cap, std, spd: cap <= 2000, 275),
        (lambda cap, std, spd: cap > 2000, None),
    ],
    "k": [
        (lambda cap, std, spd: cap <= 1350, 540),
        (lambda cap, std, spd: cap <= 1600, 590),
        (lambda cap, std, spd: cap <= 2000, 625),
        (lambda cap, std, spd: cap > 2000, None),
    ],
    "l": [
        (lambda cap, std, spd: spd < 2 and cap <= 630, 340),
        (lambda cap, std, spd: spd < 2 and cap > 630, 380),
        (lambda cap, std, spd: spd <= 2.5, 450),
        (lambda cap, std, spd: spd > 2.5, None),
    ]
}

# 主查表：Lift_Model -> 构件 -> 参数规则
csd_table = {
    "LTHX": {
        "CSD1": "a",
        "CSD2": "b",
        "CSD3": None,
        "CSD4": 85
    },
    "LTHX Car": {
        "CSD1": 370,
        "CSD2": 760,
        "CSD3": None,
        "CSD4": 165
    },
    "LTHW": {
        "CSD1": "c",
        "CSD2": "d",
        "CSD3": None,
        "CSD4": 85
    },
    "LTHW Car": {
        "CSD1": 450,
        "CSD2": 1000,
        "CSD3": None,
        "CSD4": 165
    },
    "EVIK": {
        "CSD1": "e",
        "CSD2": "f",
        "CSD3": "g",
        "CSD4": 85
    },
    "EVIN": {
        "CSD1": "h",
        "CSD2": "i",
        "CSD3": None,
        "CSD4": 85
    },
    "LTK": {
        "CSD1": "j",
        "CSD2": "k",
        "CSD3": "l",
        "CSD4": 85
    }
}

# 查询函数
def query_CSD_value(lift_model: str, component: str, capacity: int, standard: str, speed: float):
    rule_key = csd_table.get(lift_model, {}).get(component)
    if rule_key is None:
        return None
    if isinstance(rule_key, int):
        return rule_key
    if isinstance(rule_key, str):
        for cond, val in lookup_rules_CSD.get(rule_key, []):
            if cond(capacity, standard, speed):
                return val
    return None
