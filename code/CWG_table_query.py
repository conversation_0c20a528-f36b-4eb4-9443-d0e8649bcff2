

CWG_table = {
        "LTHX": {
            "SIDE": {1600: 1180, 2000: 1180, 3000: 1540, 4000: 2022, 5000: 2022},
            "REAR": {}
        },
        "LTHX Car": {
            "SIDE": {3000: 2022, 4000: 2707, 5000: 2707},
            "REAR": {}
        },
        "LTHW": {
            "SIDE": {1600: 1450, 2000: 1450, 3000: 1682, 4000: 2022, 5000: 2022},
            "REAR": {}
        },
        "LTHW Car": {
            "SIDE": {3000: 2022, 4000: 2707, 5000: 2707},
            "REAR": {}
        },
        "EVIK": {
            "SIDE": {400: 1030, 630: 1030, 800: 1030, 1000: 1030, 1150: 1030, 1250: 1030, 1350: 1180, 1600: 1180, 1800: None, 2000: None},
            "REAR": {400: 1030, 630: 1030, 800: 1030, 1000: 1030, 1150: 1030, 1250: 1030, 1350: 1180, 1600: 1180, 1800: None, 2000: None}
        },
        "EVIN": {
            "SIDE": {400: 740, 630: 1030, 800: 1030, 1000: 1030, 1150: 1030, 1250: 1030, 1350: 1180, 1600: 1180},
            "REAR": {}
        },
        "LTK": {
            "SIDE": {630: 1025, 800: 1025, 1000: 1025, 1150: 1025, 1250: 1025, 1350: 1025, 1600: 1175, 2000: 1175},
            "REAR": {630: 1025, 800: 1025, 1000: 1025, 1150: 1025, 1250: 1025, 1350: 1025, 1600: 1175, 2000: None}
        }
    }

def query_CWG_value(Lift_Model, Capacity, CWT_Position):
     # 处理 LTHX Car 和 LTHW Car 的特殊逻辑（只使用 SIDE 数据）
    if Lift_Model in ["LTHX Car", "LTHW Car"]:
        # 获取该型号对应的 SIDE 数据表
        side_table = CWG_table[Lift_Model]["SIDE"]
        # 查找对应的载重值
        return side_table.get(Capacity)
    # 其他 Lift_Model 的查找（包含 REAR 和 SIDE 数据）  
    else:
        table = CWG_table[Lift_Model][CWT_Position]
        return table.get(Capacity, None)