lookup_rules_DW0 = {
    "a": [
        (lambda std: std == "GOST 33984.1", 900),
        (lambda std: std != "GOST 33984.1", 800)
    ],
    "b": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", 800)
    ],
    "c": [
        (lambda std: std == "GOST 33984.1", 1200),
        (lambda std: std != "GOST 33984.1", 900)
    ]
}


DW0_table = {
    "LTHX": {
        "SIDE": {1600: 1300, 2000: 1600, 3000: 1800, 4000: 2100, 5000: 2400},
        "REAR": {}
    },
    "LTHX Car": {
        "SIDE": {3000: 2500, 4000: 2500, 5000: 2500},
        "REAR": {}
    },
    "LTHW": {
        "SIDE": {1600: 1300, 2000: 1600, 3000: 1800, 4000: 2100, 5000: 2400},
        "REAR": {}
    },
    "LTHW Car": {
        "SIDE": {3000: 2500, 4000: 2500, 5000: 2500},
        "REAR": {}
    },
    "EVIK": {
        "SIDE": {
            400: None, 630: 800, 800: "a", 1000: 900, 1150: 1100,
            1250: 1100, 1350: 1100, 1600: 1100
        },
        "REAR": {
            400: 700, 630: "b", 800: "a", 1000: "c", 1150: 1100,
            1250: 1100, 1350: 1100, 1600: 1100
        }
    },
    "EVIN": {
        "SIDE": {
            400: 700, 630: 800, 800: "a", 1000: 900, 1150: 1000,
            1250: 1100, 1350: 1100, 1600: 1100
        },
        "REAR": {}
    },
    "LTK": {
        "SIDE": {
            630: 800, 800: 800, 1000: 900, 1150: 1000,
            1250: 1000, 1350: 1100, 1600: 1100, 2000: 1200
        },
        "REAR": {
            630: 800, 800: 800, 1000: 900, 1150: 1000,
            1250: 1000, 1350: 1100, 1600: 1100, 2000: None
        }
    }
}

def query_DW0_value(Lift_Model, CWT_Position, Capacity, Standard):
    table = DW0_table.get(Lift_Model, {}).get(CWT_Position, {})
    val = table.get(Capacity, None)
    if isinstance(val, str):  # 动态规则
        return lookup_rules_DW0(val, Standard)
    else:
        return val