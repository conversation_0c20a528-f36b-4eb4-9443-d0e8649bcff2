

CWD_table = {
    "EVIK": {
        400: 400, 630: 400, 800: 400, 1000: 400,
        1150: 400, 1250: 400, 1350: 400, 1600: 400,
        1800: 520, 2000: 520
    },
    "EVIN": {
        400: 400, 630: 400, 800: 400, 1000: 400,
        1150: 400, 1250: 400, 1350: 400, 1600: 400
    },
    "LTK": {
        630: 400, 800: 400, 1000: 400, 1150: 400,
        1250: 400, 1350: 400, 1600: 400, 2000: 520
    }
}

lookup_rules_cwe = {
    # a - LTHX
    "a": [
        (lambda cap, door, pos: pos == "REAR", None),
        (lambda cap, door, pos: cap <= 2000 and door is True, 450),
        (lambda cap, door, pos: cap <= 2000 and door is False, 480),
        (lambda cap, door, pos: cap <= 3000 and door is True, 500),
        (lambda cap, door, pos: cap <= 3000 and door is False, 550),
        (lambda cap, door, pos: cap <= 5000, 550),
        (lambda cap, door, pos: cap > 5000, None)
    ],
    # b - LTHX Car
    "b": [
        (lambda cap, door, pos: cap <= 3000, 840),
        (lambda cap, door, pos: cap > 3000, -1040)
    ],
    # c - EVIK
    "c": [
        (lambda cap, door, pos: pos == "REAR" and cap == 400, 0),
        (lambda cap, door, pos: pos == "REAR" and cap != 400, "CWD/2"),
        (lambda cap, door, pos: pos != "REAR", "CWD/2")
    ],
    # d - EVIN
    "d": [
        (lambda cap, door, pos: pos == "REAR", None),
        (lambda cap, door, pos: pos != "REAR", "CWD/2")
    ]
}

cwe_table = {
    "LTHX": "a",
    "LTHX Car": "b",
    "LTHW": 0,
    "LTHW Car": 0,
    "EVIK": "c",
    "EVIN": "d",
    "LTK": "CWD/2"
}

def query_CWD(lift_model, capacity):
    try:
        return CWD_table[lift_model][capacity]
    except KeyError:
        return None
    
def query_CWe_value(Lift_Model, Capacity, Through_Door, CWT_Position):
    key = cwe_table.get(Lift_Model)
    cwd = query_CWD(Lift_Model, Capacity)
    if key == 0:
        return 0
    elif key == "CWD/2":
        return cwd / 2 if cwd is not None else None
    else:
        rules = lookup_rules_cwe.get(key, [])
        for cond, val in rules:
            if cond(Capacity, Through_Door, CWT_Position):
                if val == "CWD/2":
                    return cwd / 2 if cwd is not None else None
                return val
        return None
                