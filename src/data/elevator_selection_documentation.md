# 电梯选型软件文档

## 1. 软件概述

电梯选型软件是一个用于电梯设计和规划的计算工具，通过用户输入的基本参数（如梯型、载重、速度、轿厢尺寸等），自动计算出电梯的井道尺寸和其他技术参数，并提供参数验证和建议。该软件的核心是一系列复杂的计算公式和业务规则，确保电梯设计符合行业标准和技术要求。

## 2. 软件结构

软件由以下几个主要部分组成：

1. **输入界面**：用户输入基本参数
2. **计算引擎**：处理输入参数并执行计算
3. **数据库**：存储标准配置和参考数据
4. **输出界面**：显示计算结果和警告信息

## 3. 核心业务逻辑

### 3.1 参数输入与验证

用户需要输入以下基本参数：

- **梯型(Lift Model)**：如LTHX, LTHW, EVIK, EVIN, LTK等
- **载重(Capacity)**：范围从400kg到5000kg
- **速度(Speed)**：范围从0.5m/s到2.5m/s
- **轿厢尺寸(Car Width/Depth/Height)**
- **门类型(Door Opening)**和**门宽(Door Width)**
- **对重位置(CWT Position)**：SIDE或REAR
- **标准(Standard)**：如EN81-1, GOST 33984.1等

系统会对输入参数进行验证，确保：
- 载重和速度在标准配置范围内
- 轿厢尺寸符合载重要求
- 门宽适合轿厢尺寸
- 对重位置适合所选梯型

### 3.2 井道尺寸计算

井道尺寸计算基于以下原则：

1. 以轿厢中心为坐标原点
2. X方向(轿厢宽度方向)和Y方向(轿厢深度方向)分别计算
3. 考虑各种条件对井道尺寸的要求，取极值确定最小井道尺寸

计算过程考虑多种因素：
- 对重位置(SIDE或REAR)
- 门类型(C2, S2, C4等)
- 标准规范(EN81-1, GOST 33984.1等)
- 运行高度和速度
- 井道偏差值

### 3.3 载重计算与验证

系统会根据轿厢面积计算理论载重：

1. 计算轿厢面积：`面积 = (轿厢宽度 × 轿厢深度 + 门区域面积) / 1,000,000`（平方米）
2. 根据面积计算理论载重
3. 验证计算载重与用户输入载重的关系
4. 当计算载重与用户输入不匹配时提出警告

### 3.4 警告与建议生成

系统会根据计算结果生成以下类型的警告和建议：

- 载重或速度超出标准配置范围
- 轿厢尺寸不符合载重要求
- 门宽不适合轿厢尺寸
- 井道尺寸不足
- 特定梯型不适用于后置对重
- 其他技术限制和建议

## 4. 关键计算公式

### 4.1 轿厢面积计算

```
面积(平方米) = (轿厢宽度 × 轿厢深度 + 门区域系数 × 门宽 × 门高系数) / 1,000,000
```

其中，门区域系数取决于门类型和是否为贯通门。

### 4.2 载重计算

对于普通梯型：
```
载重(kg) = 根据面积查表插值计算
```

对于Car类型梯型：
```
载重(kg) = ROUNDUP(200 × 面积 / 100, 0) × 100
```

### 4.3 井道宽度计算

```
井道宽度(mm) = ROUNDUP((最大X坐标 - 最小X坐标) / 10, 0) × 10
```

其中，X坐标取决于轿厢宽度、对重位置、门类型等因素。

### 4.4 井道深度计算

类似井道宽度计算，但考虑Y方向的坐标。

### 4.5 参数验证公式

```
不适用对重后置 = 特定梯型组合 AND 对重位置为REAR
```

## 5. 数据关系

软件使用多个数据表存储标准配置和参考数据：

1. **标准配置表**：存储不同梯型、载重、速度组合的标准参数
2. **井道尺寸数据表**：存储井道偏差值、门品牌系数等参考数据
3. **载重面积关系表**：存储载重与面积的对应关系

系统通过OFFSET、MATCH等函数从这些表中查找数据，实现参数关联。

## 6. 计算流程

1. 用户输入基本参数
2. 系统验证参数有效性
3. 计算轿厢面积和对应载重
4. 验证计算载重与用户输入载重的关系
5. 计算井道尺寸要求
6. 验证各项参数是否符合标准和限制条件
7. 生成警告和建议信息
8. 显示计算结果和警告

## 7. 业务规则限制

### 7.1 梯型与参数限制

- 不同梯型有不同的载重和速度范围限制
- 特定梯型不适用于后置对重，如：
  - LTHX和LTHW不适用于后置对重
  - LTK和EVIK在载重>1600kg时不适用于后置对重
  - EVIN不适用于后置对重
  - EVIK在GOST标准下且载重=630kg时不适用于后置对重

### 7.2 门宽限制

不同门类型有不同的宽度限制：
- C2门：宽度应在600mm-1300mm之间
- S2门：宽度应在600mm-2000mm之间
- C4门：宽度应在1300mm-3000mm之间
- S3门：宽度应在800mm-2000mm之间

### 7.3 井道尺寸容差

根据运行高度(TH)确定井道偏差值：
- TH≤50m: 15mm(Normal)或40mm(Well to do)
- TH≤125m: 25mm(Normal)或60mm(Well to do)
- TH>125m: 40mm(Normal)或90mm(Well to do)

## 8. 网页实现注意事项

在将此Excel计算器转换为网页工具时，需要注意以下几点：

1. **保持计算逻辑一致**：确保JavaScript实现的计算逻辑与Excel公式完全一致
2. **数据表的处理**：将Excel中的数据表转换为JavaScript数组或对象
3. **参数验证**：实现与Excel相同的参数验证逻辑
4. **实时计算**：实现输入参数变化时的实时计算
5. **警告显示**：设计清晰的警告和建议显示机制
6. **用户界面**：设计直观的用户输入界面，类似Excel的Home工作表
7. **响应式设计**：确保网页工具在不同设备上都能正常使用

## 9. 总结

电梯选型软件是一个复杂的计算工具，通过一系列公式和业务规则，将用户输入的基本参数转换为详细的电梯规格和井道尺寸。将其转换为网页工具需要仔细实现所有计算逻辑和业务规则，确保计算结果的准确性和一致性。



