import { StaffLog, OperationType, SecurityLevel, PaginationStaffLogResponse } from '@/api/staff/types'

// 模拟员工日志数据
export const mockStaffLogsData: StaffLog[] = [
  {
    id: 'LOG001',
    staff_id: 'EMP001',
    staff_name: '张三',
    staff_department: '技术部',
    staff_position: '软件工程师',
    staff_region: '北京',
    operation_content: '查看客户信息',
    operation_type: OperationType.READ,
    operation_details: '访问了客户管理系统，查看了客户列表和详细信息',
    security_level: SecurityLevel.LOW,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-01-15 09:30:00',
    created_by: 'system'
  },
  {
    id: 'LOG002',
    staff_id: 'EMP002',
    staff_name: '李四',
    staff_department: '销售部',
    staff_position: '销售经理',
    staff_region: '上海',
    operation_content: '修改客户资料',
    operation_type: OperationType.WRITE,
    operation_details: '更新了客户联系方式和地址信息',
    security_level: SecurityLevel.MEDIUM,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    created_at: '2024-01-15 10:15:00',
    created_by: 'admin'
  },
  {
    id: 'LOG003',
    staff_id: 'EMP003',
    staff_name: '王五',
    staff_department: '财务部',
    staff_position: '财务专员',
    staff_region: '深圳',
    operation_content: '删除财务记录',
    operation_type: OperationType.WRITE,
    operation_details: '删除了过期的财务报表数据',
    security_level: SecurityLevel.HIGH,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-01-15 11:00:00',
    created_by: 'admin'
  },
  {
    id: 'LOG004',
    staff_id: 'EMP001',
    staff_name: '张三',
    staff_department: '技术部',
    staff_position: '软件工程师',
    staff_region: '北京',
    operation_content: '导出数据报表',
    operation_type: OperationType.READ,
    operation_details: '导出了月度销售数据报表',
    security_level: SecurityLevel.MEDIUM,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-01-15 14:30:00',
    created_by: 'system'
  },
  {
    id: 'LOG005',
    staff_id: 'EMP004',
    staff_name: '赵六',
    staff_department: '人事部',
    staff_position: '人事专员',
    staff_region: '广州',
    operation_content: '查看员工档案',
    operation_type: OperationType.READ,
    operation_details: '查看了新员工的入职档案和合同信息',
    security_level: SecurityLevel.MEDIUM,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    created_at: '2024-01-15 15:45:00',
    created_by: 'system'
  },
  {
    id: 'LOG006',
    staff_id: 'EMP005',
    staff_name: '钱七',
    staff_department: '技术部',
    staff_position: '系统管理员',
    staff_region: '北京',
    operation_content: '修改系统配置',
    operation_type: OperationType.WRITE,
    operation_details: '更新了系统安全策略和权限配置',
    security_level: SecurityLevel.HIGH,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Linux; Ubuntu) AppleWebKit/537.36',
    created_at: '2024-01-15 16:20:00',
    created_by: 'admin'
  },
  {
    id: 'LOG007',
    staff_id: 'EMP002',
    staff_name: '李四',
    staff_department: '销售部',
    staff_position: '销售经理',
    staff_region: '上海',
    operation_content: '查看销售报表',
    operation_type: OperationType.READ,
    operation_details: '访问了本月销售数据和业绩统计',
    security_level: SecurityLevel.LOW,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
    created_at: '2024-01-15 17:10:00',
    created_by: 'system'
  },
  {
    id: 'LOG008',
    staff_id: 'EMP006',
    staff_name: '孙八',
    staff_department: '市场部',
    staff_position: '市场专员',
    staff_region: '杭州',
    operation_content: '上传营销材料',
    operation_type: OperationType.WRITE,
    operation_details: '上传了新的产品宣传册和营销方案',
    security_level: SecurityLevel.LOW,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-01-15 18:00:00',
    created_by: 'system'
  },
  {
    id: 'LOG009',
    staff_id: 'EMP003',
    staff_name: '王五',
    staff_department: '财务部',
    staff_position: '财务专员',
    staff_region: '深圳',
    operation_content: '查看账户余额',
    operation_type: OperationType.READ,
    operation_details: '查询了公司各账户的当前余额和交易记录',
    security_level: SecurityLevel.MEDIUM,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-01-15 19:15:00',
    created_by: 'system'
  },
  {
    id: 'LOG010',
    staff_id: 'EMP007',
    staff_name: '周九',
    staff_department: '运营部',
    staff_position: '运营经理',
    staff_region: '成都',
    operation_content: '修改运营策略',
    operation_type: OperationType.WRITE,
    operation_details: '更新了产品运营策略和推广计划',
    security_level: SecurityLevel.MEDIUM,
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    created_at: '2024-01-15 20:30:00',
    created_by: 'admin'
  }
]

// 生成更多测试数据
function generateMoreLogs(): StaffLog[] {
  const operations = [
    { content: '查看用户列表', type: OperationType.READ, level: SecurityLevel.LOW, details: '浏览了系统用户列表页面' },
    { content: '修改用户权限', type: OperationType.WRITE, level: SecurityLevel.HIGH, details: '更改了用户的系统访问权限' },
    { content: '导出Excel报表', type: OperationType.READ, level: SecurityLevel.MEDIUM, details: '导出了业务数据Excel文件' },
    { content: '上传文件', type: OperationType.WRITE, level: SecurityLevel.LOW, details: '上传了工作相关文档' },
    { content: '删除过期数据', type: OperationType.WRITE, level: SecurityLevel.HIGH, details: '清理了系统中的过期数据记录' },
    { content: '查看系统日志', type: OperationType.READ, level: SecurityLevel.MEDIUM, details: '查阅了系统操作日志' },
    { content: '备份数据库', type: OperationType.WRITE, level: SecurityLevel.HIGH, details: '执行了数据库备份操作' },
    { content: '查看统计图表', type: OperationType.READ, level: SecurityLevel.LOW, details: '查看了业务数据统计图表' }
  ]

  const departments = ['技术部', '销售部', '财务部', '人事部', '市场部', '运营部', '客服部']
  const positions = ['专员', '经理', '主管', '总监', '工程师']
  const regions = ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉']
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二']

  const additionalLogs: StaffLog[] = []
  
  for (let i = 11; i <= 50; i++) {
    const operation = operations[Math.floor(Math.random() * operations.length)]
    const department = departments[Math.floor(Math.random() * departments.length)]
    const position = positions[Math.floor(Math.random() * positions.length)]
    const region = regions[Math.floor(Math.random() * regions.length)]
    const name = names[Math.floor(Math.random() * names.length)]
    
    // 生成随机日期 (过去30天)
    const date = new Date()
    date.setDate(date.getDate() - Math.floor(Math.random() * 30))
    date.setHours(Math.floor(Math.random() * 24))
    date.setMinutes(Math.floor(Math.random() * 60))
    
    additionalLogs.push({
      id: `LOG${String(i).padStart(3, '0')}`,
      staff_id: `EMP${String(i).padStart(3, '0')}`,
      staff_name: name,
      staff_department: department,
      staff_position: department + position,
      staff_region: region,
      operation_content: operation.content,
      operation_type: operation.type,
      operation_details: operation.details,
      security_level: operation.level,
      ip_address: `192.168.1.${100 + (i % 50)}`,
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      created_at: date.toISOString().replace('T', ' ').substring(0, 19),
      created_by: Math.random() > 0.5 ? 'system' : 'admin'
    })
  }
  
  return additionalLogs
}

// 合并所有日志数据
export const allMockStaffLogsData = [...mockStaffLogsData, ...generateMoreLogs()]

// 获取分页日志数据
export function getMockPaginatedStaffLogs(
  page: number,
  pageSize: number,
  searchTerm: string = '',
  operationType?: string,
  securityLevel?: string,
  department?: string
): PaginationStaffLogResponse {
  let filteredLogs = [...allMockStaffLogsData]

  // 搜索过滤
  if (searchTerm) {
    const term = searchTerm.toLowerCase()
    filteredLogs = filteredLogs.filter(log => 
      log.staff_name.toLowerCase().includes(term) ||
      log.operation_content.toLowerCase().includes(term) ||
      log.operation_details.toLowerCase().includes(term) ||
      log.staff_department.toLowerCase().includes(term) ||
      log.staff_position.toLowerCase().includes(term) ||
      (log.staff_region && log.staff_region.toLowerCase().includes(term))
    )
  }

  // 操作类型过滤
  if (operationType && operationType !== 'all') {
    filteredLogs = filteredLogs.filter(log => log.operation_type === operationType)
  }

  // 安全等级过滤
  if (securityLevel && securityLevel !== 'all') {
    filteredLogs = filteredLogs.filter(log => log.security_level === securityLevel)
  }

  // 部门过滤
  if (department && department !== 'all') {
    filteredLogs = filteredLogs.filter(log => log.staff_department === department)
  }

  // 按时间倒序排列
  filteredLogs.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

  const total = filteredLogs.length
  const totalPages = Math.ceil(total / pageSize)
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const logs = filteredLogs.slice(start, end)

  return {
    total,
    page,
    page_size: pageSize,
    total_pages: totalPages,
    logs
  }
} 