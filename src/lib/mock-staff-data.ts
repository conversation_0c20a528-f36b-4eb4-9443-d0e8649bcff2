import { Department, Position, Region, StaffDetail } from '@/api/staff/types'

// 部门数据（基于组织结构图）
export const mockDepartments: Department[] = [
  { id: 1, name: '设计团队' },
  { id: 2, name: '工程团队' },
  { id: 3, name: '售后团队' },
  { id: 4, name: '销售团队' },
  { id: 5, name: '运营部' },
]

// 区域数据（基于组织结构图）
export const mockRegions: Region[] = [
  { id: 1, name: '北部产品中心矩阵' },
  { id: 2, name: '南部产品中心矩阵' },
  { id: 3, name: '中资产品中心矩阵' },
  { id: 4, name: '岘港产品中心矩阵' },
  { id: 5, name: '河内中心' },
]

// 职位数据（基于职位信息表）
export const mockPositions: Position[] = [
  // 设计团队职位
  { id: 1, name: '设计主管' },
  { id: 2, name: '设计师' },
  
  // 工程团队职位
  { id: 3, name: '工程主管' },
  { id: 4, name: '技术员' },
  
  // 售后团队职位
  { id: 5, name: '售后主管' },
  { id: 6, name: '售后技术员' },
  
  // 销售团队职位
  { id: 7, name: '销售主管' },
  { id: 8, name: '销售支持' },
  { id: 9, name: '销售' },
  
  // 管理职位
  { id: 10, name: '产品经理' },
  { id: 11, name: '运营专员' },
]

// 员工数据（基于组织结构图和职位信息）
export const mockStaffData: StaffDetail[] = [
  // 产品经理
  {
    index: 'EMP001',
    name: 'Runnie Zhao',
    gender: 1,
    department: mockDepartments[0], // 管理层，暂时归到设计团队
    position: mockPositions[9], // 产品经理
    region: mockRegions[4], // 河内中心
    join_date: '2023-01-15',
    contract_expiry_date: '2025-01-15',
    phone: '+84-901-234-567',
    email: '<EMAIL>',
    remark: 'FLV产品经理，负责整体产品规划和管理',
    last_login: '2024-01-15',
    is_default_password: false,
  },
  
  // 设计团队
  {
    index: 'EMP002',
    name: 'Trần Đức Duy',
    gender: 1,
    department: mockDepartments[0],
    position: mockPositions[0], // 设计主管
    region: mockRegions[4], // 河内中心
    join_date: '2022-03-01',
    contract_expiry_date: '2025-03-01',
    phone: '+84-902-345-678',
    email: '<EMAIL>',
    remark: '负责团队所有设计事务，包括法规解释、标准制定、日常设计工作、销售团队沟通培训以及设计系统建立',
    last_login: '2024-01-14',
    is_default_password: false,
  },
  {
    index: 'EMP003',
    name: 'Linh',
    gender: 2,
    department: mockDepartments[0],
    position: mockPositions[1], // 设计师
    region: mockRegions[4], // 河内中心
    join_date: '2022-06-15',
    contract_expiry_date: '2025-06-15',
    phone: '+84-903-456-789',
    email: '<EMAIL>',
    remark: '资深设计师，专注于电梯系统设计',
    last_login: '2024-01-13',
    is_default_password: false,
  },
  {
    index: 'EMP004',
    name: 'Quốc Việt',
    gender: 1,
    department: mockDepartments[0],
    position: mockPositions[1], // 设计师
    region: mockRegions[4], // 河内中心
    join_date: '2022-08-01',
    contract_expiry_date: '2025-08-01',
    phone: '+84-904-567-890',
    email: '<EMAIL>',
    remark: '设计师，负责技术图纸绘制和设计方案制定',
    last_login: '2024-01-12',
    is_default_password: false,
  },
  {
    index: 'EMP005',
    name: 'Hoàng Thanh Tuấn',
    gender: 1,
    department: mockDepartments[0],
    position: mockPositions[1], // 设计师
    region: mockRegions[4], // 河内中心
    join_date: '2023-02-01',
    contract_expiry_date: '2026-02-01',
    phone: '+84-905-678-901',
    email: '<EMAIL>',
    remark: '设计师，专注于创新设计解决方案',
    last_login: '2024-01-11',
    is_default_password: true,
  },
  
  // 工程团队
  {
    index: 'EMP006',
    name: 'Runnie Zhao',
    gender: 1,
    department: mockDepartments[1],
    position: mockPositions[2], // 工程主管
    region: mockRegions[4], // 河内中心
    join_date: '2021-12-01',
    contract_expiry_date: '2024-12-01',
    phone: '+84-906-789-012',
    email: '<EMAIL>',
    remark: '工程主管，负责现场工程管理',
    last_login: '2024-01-10',
    is_default_password: false,
  },
  {
    index: 'EMP007',
    name: 'Khoa',
    gender: 1,
    department: mockDepartments[1],
    position: mockPositions[3], // 技术员
    region: mockRegions[1], // 南部产品中心矩阵
    join_date: '2022-04-15',
    contract_expiry_date: '2025-04-15',
    phone: '+84-907-890-123',
    email: '<EMAIL>',
    remark: '负责团队内所有现场工作，包括现场勘察、卸货管理、安装管理、质量管理、政府检查和交接',
    last_login: '2024-01-09',
    is_default_password: false,
  },
  {
    index: 'EMP008',
    name: 'Văn Phụng',
    gender: 1,
    department: mockDepartments[1],
    position: mockPositions[3], // 技术员
    region: mockRegions[0], // 北部产品中心矩阵
    join_date: '2022-07-01',
    contract_expiry_date: '2025-07-01',
    phone: '+84-908-901-234',
    email: '<EMAIL>',
    remark: '工程技术员，负责设备安装和调试',
    last_login: '2024-01-08',
    is_default_password: false,
  },
  {
    index: 'EMP009',
    name: 'Quy',
    gender: 1,
    department: mockDepartments[1],
    position: mockPositions[3], // 技术员
    region: mockRegions[2], // 中资产品中心矩阵
    join_date: '2023-01-01',
    contract_expiry_date: '2026-01-01',
    phone: '+84-909-012-345',
    email: '<EMAIL>',
    remark: '工程技术员，专注于电梯维护和检修',
    last_login: '2024-01-07',
    is_default_password: true,
  },
  
  // 售后团队
  {
    index: 'EMP010',
    name: 'Khoa (售后)',
    gender: 1,
    department: mockDepartments[2],
    position: mockPositions[4], // 售后主管
    region: mockRegions[4], // 河内中心
    join_date: '2021-10-01',
    contract_expiry_date: '2024-10-01',
    phone: '+84-910-123-456',
    email: '<EMAIL>',
    remark: '负责团队所有售后工作，包括各种维保和保修合同条款的管理执行、售后网点管理培训、配件销售和政府年检，售后主管同时负责各种资质工作',
    last_login: '2024-01-06',
    is_default_password: false,
  },
  {
    index: 'EMP011',
    name: 'Nguyễn Thiên Phong',
    gender: 1,
    department: mockDepartments[2],
    position: mockPositions[5], // 售后技术员
    region: mockRegions[1], // 南部产品中心矩阵
    join_date: '2022-05-15',
    contract_expiry_date: '2025-05-15',
    phone: '+84-911-234-567',
    email: '<EMAIL>',
    remark: '售后技术员，负责电梯维护保养工作',
    last_login: '2024-01-05',
    is_default_password: false,
  },
  {
    index: 'EMP012',
    name: 'Trần Văn SiL',
    gender: 1,
    department: mockDepartments[2],
    position: mockPositions[5], // 售后技术员
    region: mockRegions[0], // 北部产品中心矩阵
    join_date: '2022-09-01',
    contract_expiry_date: '2025-09-01',
    phone: '+84-912-345-678',
    email: '<EMAIL>',
    remark: '售后技术员，专注于故障排除和紧急维修',
    last_login: '2024-01-04',
    is_default_password: false,
  },
  {
    index: 'EMP013',
    name: 'Trần Hữu Hiệu',
    gender: 1,
    department: mockDepartments[2],
    position: mockPositions[5], // 售后技术员
    region: mockRegions[2], // 中资产品中心矩阵
    join_date: '2023-03-15',
    contract_expiry_date: '2026-03-15',
    phone: '+84-913-456-789',
    email: '<EMAIL>',
    remark: '售后技术员，负责定期维护和客户服务',
    last_login: '2024-01-03',
    is_default_password: true,
  },
  
  // 销售团队
  {
    index: 'EMP014',
    name: 'Runnie Zhao',
    gender: 1,
    department: mockDepartments[3],
    position: mockPositions[6], // 销售主管
    region: mockRegions[4], // 河内中心
    join_date: '2021-08-01',
    contract_expiry_date: '2024-08-01',
    phone: '+84-914-567-890',
    email: '<EMAIL>',
    remark: '销售主管，负责整体销售策略制定',
    last_login: '2024-01-02',
    is_default_password: false,
  },
  {
    index: 'EMP015',
    name: 'Mai Phong',
    gender: 2,
    department: mockDepartments[3],
    position: mockPositions[7], // 销售支持
    region: mockRegions[1], // 南部产品中心矩阵
    join_date: '2022-02-15',
    contract_expiry_date: '2025-02-15',
    phone: '+84-915-678-901',
    email: '<EMAIL>',
    remark: '负责河内中心的电梯销售，包括商机管理、客户管理、订单管理和应收账款闭环',
    last_login: '2024-01-01',
    is_default_password: false,
  },
  {
    index: 'EMP016',
    name: 'Giang',
    gender: 1,
    department: mockDepartments[3],
    position: mockPositions[8], // 销售
    region: mockRegions[0], // 北部产品中心矩阵
    join_date: '2022-04-01',
    contract_expiry_date: '2025-04-01',
    phone: '+84-916-789-012',
    email: '<EMAIL>',
    remark: '北部销售，负责北部区域市场开发',
    last_login: '2023-12-31',
    is_default_password: false,
  },
  {
    index: 'EMP017',
    name: '戴豪杰',
    gender: 1,
    department: mockDepartments[3],
    position: mockPositions[8], // 销售
    region: mockRegions[2], // 中资产品中心矩阵
    join_date: '2022-06-01',
    contract_expiry_date: '2025-06-01',
    phone: '+84-917-890-123',
    email: '<EMAIL>',
    remark: '中国销售，负责中国市场业务拓展',
    last_login: '2023-12-30',
    is_default_password: false,
  },
  
  // 运营部
  {
    index: 'EMP018',
    name: 'Operation Null',
    gender: 1,
    department: mockDepartments[4],
    position: mockPositions[10], // 运营专员
    region: mockRegions[4], // 河内中心
    join_date: '2023-05-01',
    contract_expiry_date: '2026-05-01',
    phone: '+84-918-901-234',
    email: '<EMAIL>',
    remark: '运营专员，负责日常运营管理',
    last_login: '2023-12-29',
    is_default_password: true,
  },
]

// 员工过滤类型
interface StaffFilters {
  department?: { id: number; name: string }
  position?: { id: number; name: string }
  region?: { id: number; name: string }
  gender?: number
}

// 分页数据生成函数
export function getMockPaginatedStaff(page: number, pageSize: number, search: string = '', filters: StaffFilters = {}) {
  let filteredStaff = mockStaffData
  
  // 搜索过滤
  if (search.trim()) {
    const searchTerm = search.toLowerCase()
    filteredStaff = filteredStaff.filter(staff => 
      staff.name.toLowerCase().includes(searchTerm) ||
      staff.department.name.toLowerCase().includes(searchTerm) ||
      staff.position.name.toLowerCase().includes(searchTerm) ||
      (staff.region?.name.toLowerCase().includes(searchTerm)) ||
      staff.phone.includes(searchTerm) ||
      staff.email.toLowerCase().includes(searchTerm)
    )
  }
  
  // 应用过滤器
  if (filters.department) {
    filteredStaff = filteredStaff.filter(staff => staff.department.id === filters.department!.id)
  }
  
  if (filters.position) {
    filteredStaff = filteredStaff.filter(staff => staff.position.id === filters.position!.id)
  }
  
  if (filters.region) {
    filteredStaff = filteredStaff.filter(staff => staff.region?.id === filters.region!.id)
  }
  
  if (filters.gender !== undefined) {
    filteredStaff = filteredStaff.filter(staff => staff.gender === filters.gender)
  }
  
  const total = filteredStaff.length
  const totalPages = Math.ceil(total / pageSize)
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const staffs = filteredStaff.slice(startIndex, endIndex)
  
  return {
    total,
    page,
    page_size: pageSize,
    total_pages: totalPages,
    staffs: staffs.map((staff, idx) => ({
      ...staff,
      id: startIndex + idx
    }))
  }
} 