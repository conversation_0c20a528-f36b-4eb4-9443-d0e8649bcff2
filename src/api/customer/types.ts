// 客户类型枚举
export enum CustomerType {
  OWNER = 'owner',           // 业主
  MAINCONTRACTOR = 'maincontractor',  // 总包
  BROKER = 'broker'          // 中间人
}

// 重要程度枚举
export enum ImportanceLevel {
  A = 'A',
  B = 'B', 
  C = 'C'
}

// 联系人信息
export interface ContactInfo {
  name: string;    // 联系人姓名
  phone: string;   // 联系人电话
}

// 基础客户信息
export interface CustomerBaseInfo {
  id: number;
  name: string;
  customer_type: CustomerType;  // 客户类型
  index?: string;              // 序列号
}

// 业主信息
export interface OwnerInfo {
  owner_name: string;              // 业主名称 (必填)
  owner_phone: string;             // 业主电话号码 (必填)
  contacts: ContactInfo[];         // 联系人信息 (必填1位，可填多位)
  address: string;                 // 业主详公地址 (必填)
  email?: string;                  // 业主邮箱地址
  registration_date: string;       // 登录日期 (系统自动填入)
  staff_member: string;            // 登录业务员 (系统自动填入)
  importance_level: ImportanceLevel; // 重要程度 (A/B/C级)
  remark?: string;                 // 备注信息
}

// 总包信息
export interface MaincontractorInfo {
  company_name: string;            // 总包公司名称 (必填)
  company_phone: string;           // 总包公司电话 (必填)
  contacts: ContactInfo[];         // 联系人信息 (必填1位，可填多位)
  company_address: string;         // 总包公司地址 (必填)
  company_email?: string;          // 总包公司邮箱
  registration_date: string;       // 登录日期 (系统自动填入)
  staff_member: string;            // 登录业务员 (系统自动填入)
  importance_level: ImportanceLevel; // 重要程度 (A/B/C级)
  remark?: string;                 // 备注信息
}

// 中间人信息
export interface BrokerInfo {
  broker_name: string;             // 中间人姓名 (必填)
  broker_phone: string;            // 中间人电话号码 (必填)
  broker_email?: string;           // 中间人邮箱
  registration_date: string;       // 登录日期 (系统自动填入)
  staff_member: string;            // 登录业务员 (系统自动填入)
  importance_level: ImportanceLevel; // 重要程度 (A/B/C级)
  remark?: string;                 // 备注信息
}

// 完整的客户信息
export interface FullCustomerInfo extends CustomerBaseInfo {
  owner_info?: OwnerInfo;
  maincontractor_info?: MaincontractorInfo;
  broker_info?: BrokerInfo;
}

// 客户操作接口 (用于创建和更新)
export interface CustomerOperation {
  customer_type: CustomerType;
  owner_info?: Partial<OwnerInfo>;
  maincontractor_info?: Partial<MaincontractorInfo>;
  broker_info?: Partial<BrokerInfo>;
}

// 分页客户列表响应
export interface PaginatedCustomerListResponse {
  customers: FullCustomerInfo[];
  total: number;
  page: number;
  page_size: number;
}

// 旧的接口保持兼容性
export interface EmployeeOperation {
  name: string;
  phone: string;
  email: string;
  remark: string;
}

export interface CustomerEmployee {
  name: string;
  phone: string;
  email: string;
  remark: string;
  index?: string;
}

export interface CustomerEmployeeResponse {
  customer: {
    id: number;
    name: string;
  },
  employees: {
    name: string;
    phone: string;
    email: string;
    remark: string;
    index: string;
  }[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

