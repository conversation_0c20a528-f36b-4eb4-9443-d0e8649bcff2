
// 员工选择器基本信息
export interface StaffBaseInfo {
  id: number;
  name: string;
}

// 操作类型枚举
export enum OperationType {
  READ = 'read',
  WRITE = 'write'
}

// 安全等级枚举
export enum SecurityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// 员工日志
export interface StaffLog {
  id: string;
  staff_id: string;
  staff_name: string;
  staff_department: string;
  staff_position: string;
  staff_region?: string;
  operation_content: string;
  operation_type: OperationType;
  operation_details: string;
  security_level: SecurityLevel;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  created_by?: string;
}

// 分页员工日志响应
export interface PaginationStaffLogResponse {
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
  logs: StaffLog[];
}

// 职位
export interface Position {
  id: number
  name: string
}

// 部门
export interface Department {
  id: number
  name: string
}

// 区域
export interface Region {
  id: number
  name: string
}

// 员工详情
export interface StaffDetail {
  department: Department
  position: Position
  region?: Region
  last_login: string
  index: string
  name: string
  gender: number
  join_date: string
  contract_expiry_date: any
  phone: string
  email: string
  remark: string
  is_default_password: boolean
}

export interface PaginationStaffListResponse {
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
  staffs: StaffDetail[];
}

// 员工 oper types
export interface StaffOperation {
  name: string
  gender: number
  department: number
  position: number
  region?: number
  join_date: string
  contract_expiry_date: string
  phone: string
  email: string
  remark: string
}
