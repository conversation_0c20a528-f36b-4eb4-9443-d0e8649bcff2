import request from "../request";
import { CalculateRecommendResponse, CalculateRequestOptional, CalculateResponse } from "./types";
import { ResponseData } from "../types";

export const calculateElevator = async (
  data: CalculateRequestOptional
): Promise<ResponseData<CalculateResponse>> => {
  const response = await request.post<ResponseData<CalculateResponse>>(
    "https://midea-demo.mountex.online/api/calculate/",
    data
  );
  return response.data;
};

export const calculateElevatorRecommend = async (
  data: CalculateRequestOptional
): Promise<ResponseData<CalculateRecommendResponse>> => {
  const response = await request.post<ResponseData<CalculateRecommendResponse>>(
    "https://midea-demo.mountex.online/api/calculate_recommend/",
    data
  );
  return response.data;
};
