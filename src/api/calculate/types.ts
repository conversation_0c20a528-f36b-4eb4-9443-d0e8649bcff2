export interface CalculateRequest {
  Lift_Model: string;
  Capacity: number;
  Speed: number;
  Travel_Height: number;
  Car_Width: number;
  Car_Depth: number;
  Car_Height: number;
  CWT_Position: string;
  CWT_Safety_Gear: string;
  Door_Opening: string;
  Door_Width: number;
  Door_Height: number;
  Through_Door: string;
  Glass_Door: string;
  Standard: string;
  Door_Center_from_Car_Center: string;
  Car_Area_Exceed_the_Code: string;
  Shaft_Tolerance: string;
  Marble_Floor: number;
  Shaft_Width: number;
  Shaft_Depth: number;
  Overhead: number;
  Pit_Depth: number;
}

export type CalculateRequestOptional = Partial<CalculateRequest>;

export interface CalculateResponse {
  Capacity: number;
  Persons: string;
  Shaft_Depth_min: number;
  Shaft_Height_min: number;
  Shaft_Pit_min: number;
  Shaft_Width_min: number;
  warn: boolean;
  warning_text: string[];
  Marble_Cap: string;
  Area_Cap_notCar: number;
  Area_Cap: number;
}

export interface CalculateRecommendResponse {
  Shaft_Depth_min: number;
  Shaft_Height_min: number;
  Shaft_Pit_min: number;
  Shaft_Width_min: number;
  warn: boolean;
  warning_text: string[];
}
