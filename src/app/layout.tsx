import type {Metada<PERSON>} from "next";
import {<PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono} from "next/font/google";
import "./globals.css";
import {ThemeProvider} from "@/components/self/theme-provider"
import {Toaster} from "@/components/ui/sonner"

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "MDV Dashboard",
  description: "MDV 电梯商机管理",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
            attribute="class"
            defaultTheme="light"
            forcedTheme="light"
            disableTransitionOnChange
          >
        {children}
        <Toaster position="top-center" richColors />        
        </ThemeProvider>
      </body>
    </html>
  );
}
