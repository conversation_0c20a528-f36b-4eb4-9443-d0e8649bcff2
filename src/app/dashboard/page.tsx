"use client";
import { SectionCards } from "@/components/dashboard/section-cards";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Plus,
  Calculator,
  Edit,
  ArrowRight,
  Building,
  Phone,
  Mail,
  MapPin,
  CheckCircle2,
  Clock3,
  CircleDashed,
  ReceiptText,
  Download,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Card, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { InboxIcon } from "lucide-react";
import { useDebounce } from "@/hooks/use-debounce";
import { OpportunityTable, OpportunityData } from "@/components/dashboard/opportunity-table";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ProductCategoryModal } from "@/components/dashboard/product-category-modal";
import { RefreshCw } from "lucide-react";



const newButtonGroups = {
  detail: true,
  submit: true,
  contract: true,
  hideBlueprint: true,
  hideElevator: true,
};

// Mock 数据按产品类别
const mockDataByCategory = {
  多联机: [
    {
      id: "2001",
      projectCode: "VRF-2023-0001",
      projectName: "阳光城市商业广场多联机项目",
      customerName: "广州恒信地产",
      projectManagerName: "李明",
      salesName: "张小华",
      salesAssistantName: "王五",
      progress: 65,
      status: "技术交流",
      createdAt: "2024-05-01T08:00:00Z",
      expectedSignDate: "2024-08-15T00:00:00Z",
      estimatedAmount: "380",
      elevatorCount: "42",
      projectTypeName: "商业",
      locationName: "广州",
      engineeringTypeName: "新建项目",
      industryTypeName: "商业地产",
      contactPhone: "13800138000",
      contactEmail: "<EMAIL>",
      projectAddress: "广州市天河区阳光大道188号",
      successProbability: 70,
      projectStatus: "已报价",
      statusUpdatedAt: "2024-05-02T08:00:00Z",
      buttonGroups: newButtonGroups,
    },
    {
      id: "2002",
      projectCode: "VRF-2023-0002",
      projectName: "星河湾酒店多联机改造",
      customerName: "星河湾酒店管理",
      projectManagerName: "赵刚",
      salesName: "刘芳",
      salesAssistantName: "李强",
      progress: 40,
      status: "方案设计",
      createdAt: "2024-04-15T10:30:00Z",
      expectedSignDate: "2024-07-20T00:00:00Z",
      estimatedAmount: "220",
      elevatorCount: "18",
      projectTypeName: "改造项目",
      locationName: "深圳",
      engineeringTypeName: "改造项目",
      industryTypeName: "酒店",
      contactPhone: "13900139000",
      contactEmail: "<EMAIL>",
      projectAddress: "深圳市南山区星河大道99号",
      successProbability: 45,
      projectStatus: "已出图",
      statusUpdatedAt: "2024-04-16T10:30:00Z",
      buttonGroups: newButtonGroups,
    },
  ],
  水机: [
    {
      id: "3001",
      projectCode: "CHI-2023-0001",
      projectName: "国际金融中心水冷机组",
      customerName: "国际金融控股集团",
      projectManagerName: "陈强",
      salesName: "王明",
      salesAssistantName: "李小红",
      progress: 75,
      status: "初步报价",
      createdAt: "2024-03-10T09:15:00Z",
      expectedSignDate: "2024-06-30T00:00:00Z",
      estimatedAmount: "560",
      elevatorCount: "8",
      projectTypeName: "商业",
      locationName: "上海",
      engineeringTypeName: "新建项目",
      industryTypeName: "金融",
      contactPhone: "13700137000",
      contactEmail: "<EMAIL>",
      projectAddress: "上海市浦东新区陆家嘴环路888号",
      successProbability: 80,
      projectStatus: "已签约",
      statusUpdatedAt: "2024-03-11T09:15:00Z",
      buttonGroups: newButtonGroups,
    },
    {
      id: "3002",
      projectCode: "CHI-2023-0002",
      projectName: "科技产业园水系统",
      customerName: "未来科技发展有限公司",
      projectManagerName: "黄建",
      salesName: "陈伟",
      salesAssistantName: "张丽",
      progress: 30,
      status: "需求收集",
      createdAt: "2024-04-05T14:20:00Z",
      expectedSignDate: "2024-08-10T00:00:00Z",
      estimatedAmount: "320",
      elevatorCount: "5",
      projectTypeName: "工业",
      locationName: "杭州",
      engineeringTypeName: "新建项目",
      industryTypeName: "科技园区",
      contactPhone: "13600136000",
      contactEmail: "<EMAIL>",
      projectAddress: "杭州市滨江区科技园路128号",
      successProbability: 35,
      projectStatus: "正询价",
      statusUpdatedAt: "2024-04-06T14:20:00Z",
      buttonGroups: newButtonGroups,
    },
  ],
  空压机: [
    {
      id: "4001",
      projectCode: "COM-2023-0001",
      projectName: "汽车制造厂压缩空气系统",
      customerName: "华东汽车制造有限公司",
      projectManagerName: "吴强",
      salesName: "钱程",
      salesAssistantName: "周晓",
      progress: 85,
      status: "商务谈判",
      createdAt: "2024-02-20T11:00:00Z",
      expectedSignDate: "2024-05-25T00:00:00Z",
      estimatedAmount: "420",
      elevatorCount: "12",
      projectTypeName: "工业",
      locationName: "武汉",
      engineeringTypeName: "新建项目",
      industryTypeName: "汽车制造",
      contactPhone: "13500135000",
      contactEmail: "<EMAIL>",
      projectAddress: "武汉市经济开发区汽车大道66号",
      successProbability: 90,
      projectStatus: "已首付",
      statusUpdatedAt: "2024-02-21T11:00:00Z",
      buttonGroups: newButtonGroups,
    },
    {
      id: "4002",
      projectCode: "COM-2023-0002",
      projectName: "医疗器械厂压缩空气升级",
      customerName: "精密医疗器械有限公司",
      projectManagerName: "朱明",
      salesName: "孙丽",
      salesAssistantName: "林小明",
      progress: 45,
      status: "方案优化",
      createdAt: "2024-03-25T13:40:00Z",
      expectedSignDate: "2024-07-10T00:00:00Z",
      estimatedAmount: "180",
      elevatorCount: "6",
      projectTypeName: "改造项目",
      locationName: "苏州",
      engineeringTypeName: "改造项目",
      industryTypeName: "医疗器械",
      contactPhone: "13400134000",
      contactEmail: "<EMAIL>",
      projectAddress: "苏州工业园区医疗产业园路28号",
      successProbability: 55,
      projectStatus: "图纸确认",
      statusUpdatedAt: "2024-03-26T13:40:00Z",
      buttonGroups: newButtonGroups,
    },
  ],
  储能: [
    {
      id: "5001",
      projectCode: "ESS-2023-0001",
      projectName: "太阳能发电厂储能项目",
      customerName: "新能源发展集团",
      projectManagerName: "徐强",
      salesName: "高峰",
      salesAssistantName: "马丽",
      progress: 55,
      status: "技术交流",
      createdAt: "2024-04-08T15:30:00Z",
      expectedSignDate: "2024-09-05T00:00:00Z",
      estimatedAmount: "680",
      elevatorCount: "1",
      projectTypeName: "工业",
      locationName: "青海",
      engineeringTypeName: "新建项目",
      industryTypeName: "新能源",
      contactPhone: "13300133000",
      contactEmail: "<EMAIL>",
      projectAddress: "青海省海西州德令哈市光伏产业园区",
      successProbability: 65,
      projectStatus: "已排产",
      statusUpdatedAt: "2024-04-09T15:30:00Z",
      buttonGroups: newButtonGroups,
    },
    {
      id: "5002",
      projectCode: "ESS-2023-0002",
      projectName: "商业中心备用电源系统",
      customerName: "环球商业管理有限公司",
      projectManagerName: "冯勇",
      salesName: "周红",
      salesAssistantName: "黎明",
      progress: 35,
      status: "方案设计",
      createdAt: "2024-03-18T10:15:00Z",
      expectedSignDate: "2024-08-20T00:00:00Z",
      estimatedAmount: "210",
      elevatorCount: "2",
      projectTypeName: "商业",
      locationName: "成都",
      engineeringTypeName: "新建项目",
      industryTypeName: "商业地产",
      contactPhone: "13200132000",
      contactEmail: "<EMAIL>",
      projectAddress: "成都市锦江区红星路三段99号",
      successProbability: 40,
      projectStatus: "已出图",
      statusUpdatedAt: "2024-03-19T10:15:00Z",
      buttonGroups: newButtonGroups,
    },
  ],
  电梯: [
    {
      id: "1001",
      projectCode: "ELV-2023-0001",
      projectName: "中央商务区甲级写字楼电梯项目",
      customerName: "华润置地有限公司",
      projectManagerName: "王强",
      salesName: "张伟",
      salesAssistantName: "李娜",
      progress: 70,
      status: "方案设计",
      createdAt: "2024-04-20T09:00:00Z",
      expectedSignDate: "2024-07-15T00:00:00Z",
      estimatedAmount: "450",
      elevatorCount: "16",
      projectTypeName: "商业",
      locationName: "胡志明市",
      engineeringTypeName: "新建项目",
      industryTypeName: "商业地产",
      contactPhone: "13800138001",
      contactEmail: "<EMAIL>",
      projectAddress: "胡志明市第1郡黎利大街235号",
      successProbability: 75,
      projectStatus: "已发货",
      statusUpdatedAt: "2024-04-21T09:00:00Z",
      buttonGroups: newButtonGroups,
    },
    {
      id: "1002",
      projectCode: "ELV-2023-0002",
      projectName: "豪华住宅小区电梯安装工程",
      customerName: "万科地产开发有限公司",
      projectManagerName: "陈明",
      salesName: "刘强",
      salesAssistantName: "赵丽",
      progress: 45,
      status: "技术交流",
      createdAt: "2024-03-28T14:30:00Z",
      expectedSignDate: "2024-08-30T00:00:00Z",
      estimatedAmount: "320",
      elevatorCount: "24",
      projectTypeName: "住宅",
      locationName: "河内市",
      engineeringTypeName: "新建项目",
      industryTypeName: "住宅地产",
      contactPhone: "13900139001",
      contactEmail: "<EMAIL>",
      projectAddress: "河内市海巴中区金马街88号",
      successProbability: 60,
      projectStatus: "已报价",
      statusUpdatedAt: "2024-03-29T14:30:00Z",
      buttonGroups: newButtonGroups,
    },
    {
      id: "1003",
      projectCode: "ELV-2023-0003",
      projectName: "医院专用电梯更新项目",
      customerName: "胡志明市第一人民医院",
      projectManagerName: "黄建",
      salesName: "周华",
      salesAssistantName: "吴静",
      progress: 60,
      status: "初步报价",
      createdAt: "2024-04-10T11:15:00Z",
      expectedSignDate: "2024-06-20T00:00:00Z",
      estimatedAmount: "280",
      elevatorCount: "8",
      projectTypeName: "改造项目",
      locationName: "胡志明市",
      engineeringTypeName: "改造项目",
      industryTypeName: "医疗",
      contactPhone: "13700137001",
      contactEmail: "<EMAIL>",
      projectAddress: "胡志明市第1郡阮知方街215号",
      successProbability: 85,
      projectStatus: "需求变更",
      statusUpdatedAt: "2024-04-11T11:15:00Z",
      buttonGroups: newButtonGroups,
    },
  ], // 电梯类别使用本地存储的数据
};

// 数据标准化函数 - 处理老数据的兼容性
const normalizeOpportunityData = (data: any): OpportunityData => {
  return {
    // 必要字段
    id: data.id || Math.random().toString(36).substr(2, 9),
    projectCode: data.projectCode || data.code || `ELV-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`,
    projectName: data.projectName || data.name || "未命名项目",
    customerName: data.customerName || data.customer || "未知客户",
    projectManagerName: data.projectManagerName || data.manager || "未指定",
    salesName: data.salesName || data.sales || "未指定",
    progress: typeof data.progress === 'number' ? data.progress : parseInt(data.progress) || 0,
    status: data.status || "商机获取",
    createdAt: data.createdAt || data.createTime || new Date().toISOString(),
    expectedSignDate: data.expectedSignDate || data.signDate || new Date().toISOString(),
    estimatedAmount: data.estimatedAmount || data.amount || "0",
    elevatorCount: data.elevatorCount || data.count || "0",
    projectTypeName: data.projectTypeName || data.type || "新建项目",
    
    // 可选字段 - 为老数据提供默认值
    salesAssistantName: data.salesAssistantName || data.assistant || "未指定",
    customerTypeName: data.customerTypeName || "业主",
    locationName: data.locationName || data.location || "未指定",
    projectAddress: data.projectAddress || data.address || "地址待补充",
    engineeringTypeName: data.engineeringTypeName || "新建项目",
    industryTypeName: data.industryTypeName || "商业地产",
    isLargeProjectName: data.isLargeProjectName || "否",
    contractCode: data.contractCode || "",
    contactPhone: data.contactPhone || data.phone || "待补充",
    contactEmail: data.contactEmail || data.email || "待补充",
    successProbability: data.successProbability || 50,
    projectStatus: data.projectStatus || "正询价",
    statusUpdatedAt: data.statusUpdatedAt || data.createdAt || new Date().toISOString(),
    
    // 按钮组配置
    buttonGroups: data.buttonGroups || {
      detail: true,
      submit: true,
      contract: true,
      hideBlueprint: true,
      hideElevator: true,
    },
  };
};

export default function Page() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [opportunities, setOpportunities] = useState<OpportunityData[]>([]);
  const [filter, setFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(true);

  // 使用防抖处理搜索词，默认延迟300ms
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // 添加产品类别状态
  const [activeCategory, setActiveCategory] = useState("电梯");
  
  // Modal 控制状态
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 产品类别数据
  const productCategories = [
    {
      title: "电梯",
      image: "/images/product-categories/elevator.jpg",
    },
    {
      title: "多联机",
      image: "/images/product-categories/vrf.jpg",
    },
    {
      title: "水机",
      image: "/images/product-categories/chiller.jpg",
    },
    {
      title: "空压机",
      image: "/images/product-categories/compressor.png",
    },
    {
      title: "储能",
      image: "/images/product-categories/energy-storage.jpg",
    },
  ];

  // 从本地存储加载商机数据
  useEffect(() => {
    setIsLoading(true);

    // 如果是电梯类别，优先使用本地存储的数据，没有时使用默认数据
    if (activeCategory === "电梯") {
      try {
        const savedOpportunities = localStorage.getItem("opportunities");
        if (savedOpportunities) {
          const parsedOpportunities = JSON.parse(savedOpportunities);
          // 如果localStorage中有数据但是为空数组，则使用默认数据
          if (parsedOpportunities.length > 0) {
            // 标准化老数据，确保兼容性
            const normalizedOpportunities = parsedOpportunities.map(normalizeOpportunityData);
            setOpportunities(normalizedOpportunities);
          } else {
            setOpportunities(
              mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
            );
          }
        } else {
          // localStorage中没有数据，使用默认数据
          setOpportunities(
            mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
          );
        }
      } catch (error) {
        console.error("加载商机数据失败", error);
        // 出错时也使用默认数据
        setOpportunities(
          mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
        );
      }
    } else {
      // 对于其他产品类别，使用模拟数据
      setOpportunities(
        mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
      );
    }

    setIsLoading(false);
  }, [activeCategory]);

  console.log(activeCategory, "activeCategory");
  // 产品类别切换处理函数
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
  };

  // 创建新商机
  const handleCreateOpportunity = () => {
    router.push("/create-opportunity");
  };

  // 更新商机
  const handleOpportunityUpdate = () => {
    // 重新加载数据
    if (activeCategory === "电梯") {
      try {
        const savedOpportunities = localStorage.getItem("opportunities");
        if (savedOpportunities) {
          const parsedOpportunities = JSON.parse(savedOpportunities);
          // 如果localStorage中有数据但是为空数组，则使用默认数据
          if (parsedOpportunities.length > 0) {
            // 标准化老数据，确保兼容性
            const normalizedOpportunities = parsedOpportunities.map(normalizeOpportunityData);
            setOpportunities(normalizedOpportunities);
          } else {
            setOpportunities(
              mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
            );
          }
        } else {
          // localStorage中没有数据，使用默认数据
          setOpportunities(
            mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
          );
        }
      } catch (error) {
        console.error("加载商机数据失败", error);
        // 出错时也使用默认数据
        setOpportunities(
          mockDataByCategory[activeCategory as keyof typeof mockDataByCategory] as OpportunityData[]
        );
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-12">
      {/* 标题栏 */}
      <div className="flex justify-between items-center py-4 px-4 sm:px-6 bg-white shadow-sm">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">{activeCategory}商机管理</h1>
          <Button 
            variant="outline" 
            onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            切换
          </Button>
        </div>
        <Button onClick={handleCreateOpportunity}>
          <Plus className="h-4 w-4 mr-1" />
          新建商机
        </Button>
      </div>

      {/* 商机表格列表 */}
      <div className="px-4 sm:px-6 pt-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00B4AA]"></div>
          </div>
        ) : (
          <OpportunityTable
            data={opportunities}
            onUpdate={handleOpportunityUpdate}
            categoryName={activeCategory}
          />
        )}
      </div>

      {/* 产品类别选择Modal */}
      <ProductCategoryModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        categories={productCategories}
        activeCategory={activeCategory}
        onCategoryChange={handleCategoryChange}
      />
    </div>
  );
}
