"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, CalendarIcon, Edit, Building, Phone, Mail, MapPin, Clock, CheckCircle2, Calculator, Users, Activity, StickyNote, FileText, Plus, Upload, Download, MoreVertical, UserPlus, ChevronRight, ChevronDown, Info } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { toast } from "sonner"
import { OpportunityData } from "@/components/dashboard/opportunity-card"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { MessageSquare, RefreshCcw } from "lucide-react"
import { Calendar } from "lucide-react"
import { cn } from "@/lib/utils"
import ReactFlow, {
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ConnectionMode,
  Panel,
  MarkerType,
  EdgeLabelRenderer,
  BaseEdge,
  EdgeProps,
  getBezierPath,
  BackgroundVariant,
  Handle,
  Position,
  useReactFlow,
  ReactFlowProvider,
} from 'reactflow'
import 'reactflow/dist/style.css'

// 移除自定义边组件，使用默认配置

// 自定义节点样式 - 添加Handle连接点
const nodeTypes = {
  start: ({ data }: any) => (
    <div className="relative">
      <div className="px-4 py-2 shadow-md rounded-lg bg-green-500 text-white border-2 border-green-600">
        <div className="font-bold">{data.label}</div>
      </div>
      <Handle type="source" position={Position.Right} />
    </div>
  ),
  process: ({ data }: any) => (
    <div className="relative">
      <div className={cn(
        "px-4 py-2 shadow-md rounded-lg border-2 min-w-[120px] text-center",
        data.completed ? "bg-green-100 border-green-500 text-green-700" :
        data.inProgress ? "bg-blue-100 border-blue-500 text-blue-700" :
        "bg-gray-50 border-gray-300 text-gray-600"
      )}>
        <div className="font-medium">{data.label}</div>
        {data.description && <div className="text-xs mt-1">{data.description}</div>}
      </div>
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
    </div>
  ),
  decision: ({ data }: any) => (
    <div className="relative">
      <div className={cn(
        "w-24 h-24 transform rotate-45 border-2 flex items-center justify-center",
        data.completed ? "bg-yellow-100 border-yellow-500" :
        data.inProgress ? "bg-orange-100 border-orange-500" :
        "bg-gray-50 border-gray-300"
      )}>
        <div className="transform -rotate-45 text-xs font-medium text-center leading-tight">
          {data.label}
        </div>
      </div>
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} id="yes" />
      <Handle type="source" position={Position.Bottom} id="no" />
    </div>
  ),
  stage: ({ data }: any) => (
    <div className="relative">
      <div className={cn(
        "px-6 py-3 shadow-lg rounded-xl border-3 font-bold text-lg text-white",
        data.color || "bg-blue-500 border-blue-600"
      )}>
        {data.label}
      </div>
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
      <Handle type="target" position={Position.Bottom} id="bottom" />
    </div>
  ),
  payment: ({ data }: any) => (
    <div className="relative">
      <div className="px-3 py-2 shadow-md rounded-lg bg-orange-50 border-2 border-orange-400 text-orange-700 min-w-[100px] text-center">
        <div className="font-medium text-sm">{data.label}</div>
        {data.description && <div className="text-xs mt-1 text-orange-600">{data.description}</div>}
      </div>
      <Handle type="target" position={Position.Top} />
    </div>
  )
}

// 根据美的电梯真实流程图创建完整的工作流数据
const createWorkflowData = () => {
  const nodes: Node[] = [
    // 第一阶段：商机创建和选型
    {
      id: 'start',
      type: 'start',
      position: { x: 50, y: 300 },
      data: { label: '开始', completed: true }
    },
    {
      id: 'create_opportunity',
      type: 'process',
      position: { x: 320, y: 300 },
      data: { label: '创建商机', description: '销售录入项目基本信息', completed: true }
    },
    {
      id: 'elevator_selection',
      type: 'process',
      position: { x: 640, y: 300 },
      data: { label: '电梯选型', description: '销售填写基本参数完成选型', completed: true }
    },

    // 第二阶段：规格确认和报价
    {
      id: 'spec_confirmation',
      type: 'stage',
      position: { x: 1020, y: 300 },
      data: { label: '工程团队确认规格阶段', color: 'bg-blue-500 border-blue-600', completed: true }
    },
    {
      id: 'quote_approval_stage',
      type: 'stage',
      position: { x: 1420, y: 300 },
      data: { label: '报价审批与出图阶段', color: 'bg-purple-500 border-purple-600', inProgress: true }
    },
    
    // 第三阶段：并行财务审批和图纸上传
    {
      id: 'finance_approval',
      type: 'process',
      position: { x: 1640, y: 150 },
      data: { label: '财务审批', description: '财务部门审批价格和成本', inProgress: true }
    },
    {
      id: 'drawing_upload',
      type: 'process',
      position: { x: 1640, y: 450 },
      data: { label: '图纸上传', description: '销售支持制作上传图纸' }
    },
    
    // 第四阶段：简化合同阶段
    {
      id: 'legal_approval',
      type: 'process',
      position: { x: 1860, y: 300 },
      data: { label: '法务审批', description: '法务部门审批合同条款' }
    },
    {
      id: 'contract_confirmation_stage',
      type: 'stage',
      position: { x: 2080, y: 300 },
      data: { label: '合同条款确认阶段', color: 'bg-green-500 border-green-600' }
    },

    // 第五阶段：并行施工计划和井道复核
    {
      id: 'construction_plan_upload',
      type: 'process',
      position: { x: 2300, y: 150 },
      data: { label: '施工计划上传', description: '工程主管制定并上传计划' }
    },
    {
      id: 'shaft_review',
      type: 'decision',
      position: { x: 2300, y: 450 },
      data: { label: '井道复核', description: '工程售后井道复核' }
    },
    
    // 下单阶段
    {
      id: 'order_stage',
      type: 'stage',
      position: { x: 2520, y: 300 },
      data: { label: '下单阶段', color: 'bg-red-500 border-red-600' }
    },

    // 第六阶段：开工
    {
      id: 'construction_start_stage',
      type: 'stage',
      position: { x: 2740, y: 300 },
      data: { label: '开工阶段', color: 'bg-indigo-500 border-indigo-600' }
    },

    // 第七阶段：进度管理和厂检
    {
      id: 'progress_management_stage',
      type: 'stage',
      position: { x: 2960, y: 300 },
      data: { label: '进度管理阶段', color: 'bg-cyan-500 border-cyan-600' }
    },
    {
      id: 'factory_inspection_stage',
      type: 'stage',
      position: { x: 3180, y: 300 },
      data: { label: '厂检阶段', color: 'bg-yellow-500 border-yellow-600' }
    },

    // 第八阶段：维保
    {
      id: 'maintenance_contract',
      type: 'process',
      position: { x: 3400, y: 150 },
      data: { label: '签订维保合同', description: '确定维保条款' }
    },
    {
      id: 'maintenance_planning',
      type: 'process',
      position: { x: 3400, y: 450 },
      data: { label: '维保计划管理', description: '制定维保排班' }
    },
    {
      id: 'maintenance_stage',
      type: 'stage',
      position: { x: 3620, y: 300 },
      data: { label: '维保阶段', color: 'bg-emerald-500 border-emerald-600' }
    },

    // 维保子流程
    {
      id: 'emergency_call',
      type: 'process',
      position: { x: 3840, y: 150 },
      data: { label: '召修跟踪', description: '400电话接收召修' }
    },
    {
      id: 'service_evaluation',
      type: 'process',
      position: { x: 3840, y: 300 },
      data: { label: '召修评价', description: '服务评价及记录' }
    },
    {
      id: 'service_record',
      type: 'process',
      position: { x: 3840, y: 450 },
      data: { label: '召修记录', description: '记录上传到系统' }
    }
  ]

  const edges: Edge[] = [
    // 主流程
    { 
      id: 'e1', 
      source: 'start', 
      target: 'create_opportunity',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '项目启动'
    },
    { 
      id: 'e2', 
      source: 'create_opportunity', 
      target: 'elevator_selection',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '商机录入完成'
    },
    { 
      id: 'e3', 
      source: 'elevator_selection', 
      target: 'spec_confirmation',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '选型完成'
    },
    { 
      id: 'e4', 
      source: 'spec_confirmation', 
      target: 'quote_approval_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '规格确认'
    },

    // 并行财务审批和图纸上传
    { 
      id: 'e5', 
      source: 'quote_approval_stage', 
      target: 'finance_approval',
      style: { stroke: '#8b5cf6', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#8b5cf6' },
      label: '财务审批'
    },
    { 
      id: 'e6', 
      source: 'quote_approval_stage', 
      target: 'drawing_upload',
      style: { stroke: '#8b5cf6', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#8b5cf6' },
      label: '图纸制作'
    },

    // 合并到法务审批
    { 
      id: 'e7', 
      source: 'finance_approval', 
      target: 'legal_approval',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '财务完成'
    },
    { 
      id: 'e8', 
      source: 'drawing_upload', 
      target: 'legal_approval',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '图纸完成'
    },

    // 法务审批到合同确认
    { 
      id: 'e9', 
      source: 'legal_approval', 
      target: 'contract_confirmation_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '法务审批完成'
    },

    // 并行施工计划上传和井道复核
    { 
      id: 'e10', 
      source: 'contract_confirmation_stage', 
      target: 'construction_plan_upload',
      style: { stroke: '#22c55e', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#22c55e' },
      label: '施工计划'
    },
    { 
      id: 'e11', 
      source: 'contract_confirmation_stage', 
      target: 'shaft_review',
      style: { stroke: '#22c55e', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#22c55e' },
      label: '井道复核'
    },

    // 井道复核通过到下单
    { 
      id: 'e12', 
      source: 'shaft_review', 
      target: 'order_stage',
      style: { stroke: '#22c55e', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#22c55e' },
      label: '复核通过',
      sourceHandle: 'yes'
    },

    // 井道复核不通过，回到规格确认 - 从下方路由避免交叉
    { 
      id: 'e13', 
      source: 'shaft_review', 
      target: 'spec_confirmation',
      style: { stroke: '#ef4444', strokeWidth: 3, strokeDasharray: '5,5' },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#ef4444' },
      label: '复核不通过',
      type: 'smoothstep',
      sourceHandle: 'no',
      targetHandle: 'bottom',
      pathOptions: { 
        offset: 150,
        borderRadius: 25
      }
    },

    // 施工计划完成到开工（需要两个条件都满足）
    { 
      id: 'e14', 
      source: 'construction_plan_upload', 
      target: 'construction_start_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '计划上传完成'
    },
    { 
      id: 'e15', 
      source: 'order_stage', 
      target: 'construction_start_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '下单完成'
    },

    // 后续流程
    { 
      id: 'e16', 
      source: 'construction_start_stage', 
      target: 'progress_management_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '开工完成'
    },
    { 
      id: 'e17', 
      source: 'progress_management_stage', 
      target: 'factory_inspection_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '进度完成'
    },

    // 维保并行
    { 
      id: 'e18', 
      source: 'factory_inspection_stage', 
      target: 'maintenance_contract',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '厂检通过'
    },
    { 
      id: 'e19', 
      source: 'factory_inspection_stage', 
      target: 'maintenance_planning',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '验收移交'
    },
    { 
      id: 'e20', 
      source: 'maintenance_contract', 
      target: 'maintenance_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '合同签订'
    },
    { 
      id: 'e21', 
      source: 'maintenance_planning', 
      target: 'maintenance_stage',
      style: { stroke: '#0ea5e9', strokeWidth: 3 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#0ea5e9' },
      label: '排班确定'
    },

    // 维保子流程
    { 
      id: 'e22', 
      source: 'maintenance_stage', 
      target: 'emergency_call',
      style: { stroke: '#10b981', strokeWidth: 2 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#10b981' },
      label: '召修服务'
    },
    { 
      id: 'e23', 
      source: 'emergency_call', 
      target: 'service_evaluation',
      style: { stroke: '#10b981', strokeWidth: 2 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#10b981' },
      label: '服务完成'
    },
    { 
      id: 'e24', 
      source: 'service_evaluation', 
      target: 'service_record',
      style: { stroke: '#10b981', strokeWidth: 2 },
      markerEnd: { type: MarkerType.ArrowClosed, color: '#10b981' },
      label: '评价完成'
    }
  ]

  return { nodes, edges }
}

// 模拟状态流转历史数据 - 基于优化后的美的电梯流程
const STATUS_HISTORY = [
  {
    id: '1',
    nodeId: 'start',
    status: '已完成',
    statusType: 'completed',
    note: '新项目已成功启动，开始商机管理流程',
    updateTime: '2024-04-15 09:00',
    operator: '系统'
  },
  {
    id: '2',
    nodeId: 'create_opportunity',
    status: '已完成',
    statusType: 'completed',
    note: '销售人员已完成商机创建，录入项目基本信息包括客户信息、项目地址、预算等',
    updateTime: '2024-04-15 10:30',
    operator: '张销售'
  },
  {
    id: '3',
    nodeId: 'elevator_selection',
    status: '已完成',
    statusType: 'completed',
    note: '电梯选型完成，已填写基本参数包括载重、速度、层站数等，系统已生成初步配置方案',
    updateTime: '2024-04-16 14:20',
    operator: '张销售'
  },
  {
    id: '4',
    nodeId: 'spec_confirmation',
    status: '已完成',
    statusType: 'completed',
    note: '工程团队已确认技术规格，验证了井道尺寸与电梯配置的匹配性，规格参数已锁定',
    updateTime: '2024-04-18 11:45',
    operator: '李工程师'
  },
  {
    id: '5',
    nodeId: 'quote_approval_stage',
    status: '已完成',
    statusType: 'completed',
    note: '报价审批与出图阶段完成，工程团队制作完成技术图纸，财务部门完成成本核算',
    updateTime: '2024-04-20 16:30',
    operator: '王工程主管'
  },
  {
    id: '6',
    nodeId: 'finance_approval',
    status: '进行中',
    statusType: 'in_progress',
    note: '财务部门正在审批项目价格和成本，评估盈利能力和风险控制',
    updateTime: '2024-04-22 09:15',
    operator: '赵财务'
  },
  {
    id: '7',
    nodeId: 'drawing_upload',
    status: '进行中',
    statusType: 'in_progress',
    note: '销售支持正在制作和上传详细技术图纸，包括安装图纸和维保说明文档',
    updateTime: '2024-04-22 09:30',
    operator: '陈销售支持'
  },
  {
    id: '8',
    nodeId: 'legal_approval',
    status: '等待中',
    statusType: 'pending',
    note: '等待法务部门审批合同条款，确保合同条款符合法律法规要求',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '9',
    nodeId: 'contract_confirmation_stage',
    status: '等待中',
    statusType: 'pending',
    note: '等待进入合同条款确认阶段，需要与客户确认最终合同条款和付款方式',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '10',
    nodeId: 'construction_plan_upload',
    status: '等待中',
    statusType: 'pending',
    note: '等待工程主管制定并上传详细施工计划，包括施工时间表和资源安排',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '11',
    nodeId: 'shaft_review',
    status: '等待中',
    statusType: 'pending',
    note: '等待工程售后进行井道复核，确认井道尺寸和条件是否符合电梯安装要求',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '12',
    nodeId: 'order_stage',
    status: '等待中',
    statusType: 'pending',
    note: '等待进入下单阶段，将收取定金并向制造商下达生产订单',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '13',
    nodeId: 'construction_start_stage',
    status: '等待中',
    statusType: 'pending',
    note: '开工阶段待启动，需要施工计划和井道复核都完成后才能开始施工',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '14',
    nodeId: 'progress_management_stage',
    status: '等待中',
    statusType: 'pending',
    note: '进度管理阶段，工程售后将收取进度款并监控安装进度',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '15',
    nodeId: 'factory_inspection_stage',
    status: '等待中',
    statusType: 'pending',
    note: '厂检阶段，工程主管将申请厂检并整改不合格项',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  },
  {
    id: '16',
    nodeId: 'maintenance_stage',
    status: '等待中',
    statusType: 'pending',
    note: '维保阶段，将签订维保合同并制定维保计划',
    updateTime: '2024-04-22 10:00',
    operator: '系统'
  }
]

// ReactFlow工作流组件内部实现
function ReactFlowWorkflowInner({ 
  onNodeClick,
  activeNodeId,
  statusHistory 
}: {
  onNodeClick: (nodeId: string) => void
  activeNodeId: string | null
  statusHistory: typeof STATUS_HISTORY
}) {
  const { nodes: initialNodes, edges: initialEdges } = createWorkflowData()
  
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const reactFlowInstance = useReactFlow()
  const [isLegendCollapsed, setIsLegendCollapsed] = useState(false)
  
  // 调试信息
  console.log('ReactFlow - 节点数量:', nodes.length)
  console.log('ReactFlow - 边数量:', edges.length)
  console.log('ReactFlow - 所有节点ID:', nodes.map(n => n.id))
  console.log('ReactFlow - 所有边:', edges.map(e => `${e.id}: ${e.source} -> ${e.target}`))
  console.log('ReactFlow - 前3个边:', edges.slice(0, 3))

  // 更新节点状态
  useEffect(() => {
    console.log('Nodes:', nodes.length, 'Edges:', edges.length)
    console.log('First few edges:', edges.slice(0, 3))
    
    setNodes((nds) =>
      nds.map((node) => {
        const history = statusHistory.find(h => h.nodeId === node.id)
        const isActive = activeNodeId === node.id
        
        return {
          ...node,
          data: {
            ...node.data,
            completed: history?.statusType === 'completed',
            inProgress: history?.statusType === 'in_progress',
          },
          style: {
            ...node.style,
            outline: isActive ? '3px solid #3b82f6' : 'none',
            outlineOffset: isActive ? '2px' : '0',
          }
        }
      })
    )
  }, [activeNodeId, statusHistory, setNodes, nodes.length, edges.length])

  // 当activeNodeId变化时，将对应节点移动到视图中心
  useEffect(() => {
    if (activeNodeId && reactFlowInstance) {
      const targetNode = nodes.find(node => node.id === activeNodeId)
      if (targetNode) {
        // 将节点移动到视图中心
        reactFlowInstance.setCenter(targetNode.position.x, targetNode.position.y, { zoom: 1, duration: 800 })
      }
    }
  }, [activeNodeId, nodes, reactFlowInstance])

  const onNodeClickHandler = useCallback((event: React.MouseEvent, node: Node) => {
    onNodeClick(node.id)
  }, [onNodeClick])

  return (
    <div style={{ width: '100%', height: '850px' }}>
      <style>{`
        /* 强制显示边连接线 */
        .react-flow__edge {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          pointer-events: all !important;
        }
        
        .react-flow__edge-path {
          stroke: #000000 !important;
          stroke-width: 3px !important;
          fill: none !important;
          stroke-linecap: round !important;
          stroke-linejoin: round !important;
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        .react-flow__edge-text {
          fill: #000000 !important;
          font-size: 12px !important;
          font-weight: 500 !important;
          display: block !important;
          visibility: visible !important;
        }
        
        .react-flow__arrowhead {
          fill: #000000 !important;
          stroke: #000000 !important;
          display: block !important;
          visibility: visible !important;
        }
        
        /* 确保SVG边元素可见 */
        svg.react-flow__edge {
          overflow: visible !important;
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        /* 强制显示所有边相关元素 */
        .react-flow__edges {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        .react-flow__edge g {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        /* 覆盖任何可能隐藏边的样式 */
        .react-flow__edge * {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        /* 确保路径元素强制显示 */
        path[class*="react-flow__edge"] {
          stroke: #000000 !important;
          stroke-width: 3px !important;
          fill: none !important;
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        /* 强制显示箭头标记 */
        marker[id*="react-flow__arrowhead"] {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        marker[id*="react-flow__arrowhead"] path {
          fill: #000000 !important;
          stroke: #000000 !important;
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
      `}</style>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={onNodeClickHandler}
        nodeTypes={nodeTypes}
        connectionMode={ConnectionMode.Loose}
        fitView
        fitViewOptions={{ 
          padding: 0.05,
          minZoom: 0.6,
          maxZoom: 2.0,
          includeHiddenNodes: false
        }}
        defaultViewport={{ x: 0, y: 0, zoom: 1.0 }}
        proOptions={{ hideAttribution: true }}
        defaultEdgeOptions={{
          style: { 
            strokeWidth: 3, 
            stroke: '#000000',
            strokeLinecap: 'round',
            strokeLinejoin: 'round'
          },
          markerEnd: { 
            type: MarkerType.ArrowClosed,
            color: '#000000'
          }
        }}
        style={{ 
          background: 'white',
          width: '100%',
          height: '100%'
        }}
        onConnect={(params) => console.log('Edge connected:', params)}
        onInit={() => console.log('ReactFlow initialized')}
      >
        <Controls />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
        <Panel position="top-right">
          <div className="bg-white rounded shadow">
            {/* 折叠状态 - 只显示图标按钮 */}
            {isLegendCollapsed ? (
              <button
                onClick={() => setIsLegendCollapsed(false)}
                className="p-2 hover:bg-gray-50 rounded transition-colors duration-200 flex items-center gap-1"
                title="展开流程图例"
              >
                <Info className="w-4 h-4 text-blue-600" />
                <ChevronDown className="w-3 h-3 text-gray-400" />
              </button>
            ) : (
              /* 展开状态 - 显示完整图例 */
              <div className="p-3 text-xs space-y-2 min-w-[160px] max-h-[400px] overflow-y-auto">
                {/* 标题栏带折叠按钮 */}
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-center flex-1">美的电梯流程图例</div>
                  <button
                    onClick={() => setIsLegendCollapsed(true)}
                    className="p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                    title="折叠图例"
                  >
                    <ChevronRight className="w-3 h-3 text-gray-400" />
                  </button>
                </div>
                
                <div className="font-medium mb-2 pt-2 border-t">节点类型</div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>起始节点</span>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-3 h-3 bg-gray-100 border border-gray-300 rounded"></div>
                  <span>处理节点</span>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-3 h-3 bg-yellow-100 border border-yellow-400 rounded transform rotate-45"></div>
                  <span>决策节点</span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span>阶段节点</span>
                </div>
                
                <div className="font-medium mb-2 pt-2 border-t">节点状态</div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-3 h-3 bg-green-100 border border-green-500 rounded"></div>
                  <span>已完成</span>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-3 h-3 bg-blue-100 border border-blue-500 rounded"></div>
                  <span>进行中</span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-gray-50 border border-gray-300 rounded"></div>
                  <span>等待中</span>
                </div>
                
                <div className="font-medium mb-2 pt-2 border-t">连接线类型</div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-4 h-0.5 bg-sky-500"></div>
                  <span>主流程</span>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-4 h-0.5 bg-green-500"></div>
                  <span>是/通过</span>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-4 h-0.5 bg-red-500"></div>
                  <span>否/不通过</span>
                </div>
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-4 h-0.5 bg-emerald-500"></div>
                  <span>维保流程</span>
                </div>
                
                <div className="font-medium mb-2 pt-2 border-t">业务阶段</div>
                <div className="text-xs space-y-1">
                  <div>• 商机创建与选型</div>
                  <div>• 规格确认与报价</div>
                  <div>• 合同确认与下单</div>
                  <div>• 施工管理与厂检</div>
                  <div>• 维保服务管理</div>
                </div>
              </div>
            )}
          </div>
        </Panel>
      </ReactFlow>
    </div>
  )
}

// ReactFlow工作流组件包装器
function ReactFlowWorkflow({ 
  onNodeClick,
  activeNodeId,
  statusHistory 
}: {
  onNodeClick: (nodeId: string) => void
  activeNodeId: string | null
  statusHistory: typeof STATUS_HISTORY
}) {
  return (
    <ReactFlowProvider>
      <ReactFlowWorkflowInner 
        onNodeClick={onNodeClick}
        activeNodeId={activeNodeId}
        statusHistory={statusHistory}
      />
    </ReactFlowProvider>
  )
}

// 状态流转历史列表组件
function StatusHistoryList({ 
  statusHistory, 
  onHistoryItemClick,
  activeHistoryId 
}: {
  statusHistory: typeof STATUS_HISTORY
  onHistoryItemClick: (historyItem: typeof STATUS_HISTORY[0]) => void
  activeHistoryId: string | null
}) {
  const getStatusBadge = (statusType: string) => {
    switch(statusType) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-700 border-green-200">已完成</Badge>
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-700 border-blue-200">进行中</Badge>
      case 'pending':
        return <Badge className="bg-gray-100 text-gray-600 border-gray-200">等待中</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getNodeName = (nodeId: string) => {
    const { nodes } = createWorkflowData()
    const node = nodes.find(n => n.id === nodeId)
    return node?.data?.label || nodeId
  }

  return (
    <div className="space-y-3">
      {statusHistory.map((item) => (
        <div 
          key={item.id}
          className={cn(
            "border rounded-lg p-4 cursor-pointer transition-all hover:border-gray-300",
            activeHistoryId === item.id ? "border-blue-500 bg-blue-50" : "border-gray-200"
          )}
          onClick={() => onHistoryItemClick(item)}
        >
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium">{getNodeName(item.nodeId)}</h4>
                {getStatusBadge(item.statusType)}
              </div>
              <p className="text-sm text-gray-600">{item.note}</p>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <div className="h-5 w-5 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-medium">
                {item.operator === '系统' ? '系' : item.operator[0]}
              </div>
              <span>{item.operator}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3.5 w-3.5" />
              <span>{item.updateTime}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default function OpportunityDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [opportunity, setOpportunity] = useState<OpportunityData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeNodeId, setActiveNodeId] = useState<string | null>(null)
  const [activeHistoryId, setActiveHistoryId] = useState<string | null>(null)

  useEffect(() => {
    const fetchOpportunityDetails = () => {
      try {
        const id = params.id as string
        // 从localStorage获取所有商机
        const opportunities = JSON.parse(localStorage.getItem('opportunities') || '[]')
        // 找到当前id的商机
        let currentOpportunity = opportunities.find((opp: OpportunityData) => opp.id === id)
        
        // 如果没有找到商机，使用默认Mock数据
        if (!currentOpportunity) {
          currentOpportunity = {
            id: id,
            projectCode: `PRJ-${id}`,
            projectName: "胡志明市Ocean Park项目电梯采购与安装",
            customerName: "华阳客户",
            projectManagerName: "陈经理",
            salesName: "赵一",
            salesAssistantName: "李助理",
            progress: 45,
            status: "电梯选型",
            createdAt: "2024-01-15T08:00:00.000Z",
            expectedSignDate: "2024-03-15T08:00:00.000Z",
            estimatedAmount: "2500",
            elevatorCount: "12",
            projectTypeName: "商业综合体",
            customerTypeName: "业主",
            locationName: "胡志明市",
            projectAddress: "胡志明市第7郡阮氏明開街Ocean Park综合体",
            engineeringTypeName: "新建工程",
            industryTypeName: "房地产开发",
            isLargeProjectName: "是",
            contractCode: "MDV-VN-2024-001",
            contactPhone: "+84 28 3899 8888",
            contactEmail: "<EMAIL>",
            successProbability: 75,
            projectStatus: "进行中"
          }
          
          // 将默认数据保存到localStorage以便后续使用
          const updatedOpportunities = [...opportunities, currentOpportunity]
          localStorage.setItem('opportunities', JSON.stringify(updatedOpportunities))
        }
        
        setOpportunity(currentOpportunity)
      } catch (error) {
        console.error("获取商机详情失败:", error)
        toast.error("获取商机详情失败")
      } finally {
        setIsLoading(false)
      }
    }

    fetchOpportunityDetails()
  }, [params.id, router])

  // 处理流程图节点点击
  const handleNodeClick = (nodeId: string) => {
    setActiveNodeId(nodeId)
    
    // 查找对应的历史记录
    const historyItem = STATUS_HISTORY.find(h => h.nodeId === nodeId)
    if (historyItem) {
      setActiveHistoryId(historyItem.id)
    } else {
      setActiveHistoryId(null)
    }
  }

  // 处理历史记录点击
  const handleHistoryItemClick = (historyItem: typeof STATUS_HISTORY[0]) => {
    setActiveNodeId(historyItem.nodeId)
    setActiveHistoryId(historyItem.id)
  }

  // 获取优先级标签样式
  const getPriorityBadge = (priority: string) => {
    switch(priority) {
      case '高':
        // 使用 Midea 红色系
        return <Badge variant="outline" className="bg-[#F8BBD0]/50 text-[#E91E63] border-[#F8BBD0]">高</Badge>
      case '中':
        // 使用 Midea 橙色系
        return <Badge variant="outline" className="bg-[#FFECB3]/50 text-[#FF9800] border-[#FFECB3]">中</Badge>
      case '低':
        // 使用 Midea 青色系
        return <Badge variant="outline" className="bg-[#B2DFDB]/50 text-[#00B4AA] border-[#B2DFDB]">低</Badge>
      default:
        // 使用 Midea 灰色系
        return <Badge variant="outline" className="bg-[#E8E8E8]/50 text-[#505050] border-[#E8E8E8]">未知</Badge>
    }
  }

  // 获取阶段标签样式
  const getStageBadge = (stage: string) => {
    // 映射到 Midea 强调色
    const stageStyles: Record<string, string> = {
      "需求收集": "bg-[#B2EBF2]/50 text-[#0092D8] border-[#B2EBF2]", // Midea 浅蓝 / 主蓝
      "方案设计": "bg-[#D1C4E9]/50 text-[#8353B4] border-[#D1C4E9]", // Midea 浅紫 / 紫
      "技术交流": "bg-[#FFF9C4]/50 text-[#FFC107] border-[#FFF9C4]", // Midea 浅黄 / 黄 (调整文本色)
      "初步报价": "bg-[#F8BBD0]/50 text-[#E91E63] border-[#F8BBD0]", // Midea 浅粉 / 红
      "方案优化": "bg-[#B2DFDB]/50 text-[#00B4AA] border-[#B2DFDB]", // Midea 浅青 / 青
      "投标确认": "bg-[#D1C4E9]/50 text-[#8353B4] border-[#D1C4E9]", // 复用浅紫 / 紫
      "商务谈判": "bg-[#B2DFDB]/50 text-[#00B4AA] border-[#B2DFDB]", // 复用浅青 / 青
      "合同签订": "bg-[#B2EBF2]/50 text-[#0092D8] border-[#B2EBF2]", // 复用浅蓝 / 主蓝
      "设计确认": "bg-[#F8BBD0]/50 text-[#E91E63] border-[#F8BBD0]", // 复用浅粉 / 红
      "生产制造": "bg-[#FFF9C4]/50 text-[#FFC107] border-[#FFF9C4]", // 复用浅黄 / 黄
      "安装交付": "bg-[#FFECB3]/50 text-[#FF9800] border-[#FFECB3]", // Midea 浅橙 / 橙
      "售后服务": "bg-[#B2DFDB]/50 text-[#00B4AA] border-[#B2DFDB]"  // 复用浅青 / 青
    }
    
    // 默认使用 Midea 灰色系
    return <Badge variant="outline" className={stageStyles[stage] || "bg-[#E8E8E8]/50 text-[#505050] border-[#E8E8E8]"}>
      {stage}
    </Badge>
  }

  // 模拟备注数据
  const notes = [
    {
      author: '李工程师',
      content: '客户对价格非常敏感，需要优化方案降低成本。建议考虑国产部件替代进口部件。',
      time: '04-25 16:45',
      tags: ['价格敏感', '成本控制']
    },
    {
      author: '张销售',
      content: '客户决策周期较长，需要提前准备多套方案以应对可能的需求变更。',
      time: '04-23 11:30',
      tags: ['决策慢', '备选方案']
    }
  ];

  // 模拟文档数据
  const documents = [
    {
      name: '项目需求说明书.pdf',
      type: 'pdf',
      size: '2.4 MB',
      uploadedAt: '04-20'
    },
    {
      name: '初步方案设计.doc',
      type: 'doc',
      size: '1.8 MB',
      uploadedAt: '04-22'
    },
    {
      name: '电梯配置表.xls',
      type: 'xls',
      size: '756 KB',
      uploadedAt: '04-24'
    },
    {
      name: '项目进度计划.ppt',
      type: 'ppt',
      size: '3.2 MB',
      uploadedAt: '04-25'
    }
  ];

  // 模拟联系人数据
  const contacts = [
    {
      name: '王建国',
      position: '项目经理',
      phone: '13812345678',
      email: '<EMAIL>',
      wechat: 'wjg_pm'
    },
    {
      name: '李明',
      position: '技术主管',
      phone: '13987654321',
      email: '<EMAIL>'
    }
  ];

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0092D8]"></div>
          <p className="text-sm text-[#505050]">加载中...</p>
        </div>
      </div>
    )
  }

  // 如果没有找到商机数据
  if (!opportunity) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-4">
        <div className="text-4xl">😢</div>
        <h1 className="text-2xl font-bold">未找到商机</h1>
        <p className="text-[#505050]">该商机可能已被删除或不存在</p>
        <Button onClick={() => router.push('/dashboard')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回列表
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button 
          className='ml-2'
            variant="ghost" 
            size="sm" 
            onClick={() => router.push('/dashboard')}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回
          </Button>
          <h1 className="text-2xl font-bold">{opportunity.projectName}</h1>
        </div>
        <div className="flex gap-2 mr-2 ml-2">
          <Button 
            variant="outline"
            onClick={() => router.push(`/dashboard/opportunity/${params.id}/elevator-selection`)}
          >
            <Calculator className="h-4 w-4 mr-2" />
            生成报价
          </Button>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            编辑详情
          </Button>
        </div>
      </div>



      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-2">
        {/* 第一列：项目概况 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">项目概况</CardTitle>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Edit className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-2 pt-0">
            <div className="space-y-1.5">
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">项目编号</span>
                <span className="text-sm font-medium">{opportunity.projectCode}</span>
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">当前阶段</span>
                <div>{getStageBadge(opportunity.status)}</div>
              </div>
              <Separator className="my-1" />
              <div className="flex flex-col gap-1 py-1">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-[#505050]">成交几率</span>
                  <span className="text-sm font-medium text-green-600">75%</span>
                </div>
                <Progress value={75} className="h-1.5 [&>div]:bg-green-500" />
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">项目类型</span>
                <span className="text-sm font-medium">{opportunity.projectTypeName}</span>
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">重要程度</span>
                <Badge variant="outline" className="border-orange-300 text-orange-600 bg-orange-50 text-xs">
                  A级
                </Badge>
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">电梯总台数</span>
                <span className="text-sm font-medium">12 台</span>
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">电梯类型</span>
                <div className="flex gap-1">
                  <Badge variant="secondary" className="text-xs">乘客电梯</Badge>
                  <Badge variant="secondary" className="text-xs">货梯</Badge>
                </div>
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">预计设备预算</span>
                <span className="text-sm font-medium">{opportunity.estimatedAmount} 万元</span>
              </div>
              <Separator className="my-1" />
              <div className="flex justify-between items-center py-1">
                <span className="text-sm text-[#505050]">预计签约日期</span>
                <div className="flex items-center gap-1 text-sm font-medium">
                  <CalendarIcon className="h-3 w-3 text-[#505050]" />
                  {new Date(opportunity.expectedSignDate).toLocaleDateString()}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 第二列：客户信息 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">客户信息</CardTitle>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Edit className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-2 pt-0">
            <div className="flex items-center p-3 bg-[#E8E8E8]/30 rounded-lg mb-3">
              <div className="h-10 w-10 rounded-full bg-[#0092D8]/10 flex items-center justify-center mr-3">
                <Building className="h-5 w-5 text-[#0092D8]" />
              </div>
              <div>
                <h3 className="font-medium text-sm">{opportunity.customerName}</h3>
                <p className="text-xs text-[#505050]">{opportunity.customerTypeName || "客户"}</p>
              </div>
            </div>

            <div className="space-y-1.5">
              <div className="flex items-start gap-2 py-1">
                <MapPin className="h-3.5 w-3.5 text-[#505050] mt-0.5" />
                <div className="flex-1">
                  <p className="text-xs font-medium">项目地址</p>
                  <p className="text-xs text-[#505050]">{opportunity.projectAddress || "胡志明市第7郡阮氏明開街"}</p>
                  <Badge variant="outline" className="font-normal border-[#E8E8E8] text-[#505050] text-xs mt-1">
                    胡志明市 (TP. Hồ Chí Minh)
                  </Badge>
                </div>
              </div>
              <Separator className="my-1" />
              <div className="flex items-start gap-2 py-1">
                <Phone className="h-3.5 w-3.5 text-[#505050] mt-0.5" />
                <div>
                  <p className="text-xs font-medium">客户电话</p>
                  <p className="text-xs text-[#505050]">{opportunity.contactPhone || "+84 28 3899 8888"}</p>
                </div>
              </div>
              <Separator className="my-1" />
              <div className="flex items-start gap-2 py-1">
                <Mail className="h-3.5 w-3.5 text-[#505050] mt-0.5" />
                <div>
                  <p className="text-xs font-medium">联系人邮箱</p>
                  <p className="text-xs text-[#505050]">{opportunity.contactEmail || "<EMAIL>"}</p>
                </div>
              </div>
              <Separator className="my-1" />
              <div className="flex items-start gap-2 py-1">
                <Users className="h-3.5 w-3.5 text-[#505050] mt-0.5" />
                <div>
                  <p className="text-xs font-medium">客户项目经理</p>
                  <p className="text-xs text-[#505050]">陈经理</p>
                </div>
              </div>
              <Separator className="my-1" />
              <div className="flex items-start gap-2 py-1">
                <Users className="h-3.5 w-3.5 text-[#505050] mt-0.5" />
                <div>
                  <p className="text-xs font-medium">对接销售</p>
                  <p className="text-xs text-[#505050]">赵一</p>
                </div>
              </div>
              <Separator className="my-1" />
              <div className="flex items-start gap-2 py-1">
                <Building className="h-3.5 w-3.5 text-[#505050] mt-0.5" />
                <div>
                  <p className="text-xs font-medium">总包方</p>
                  <p className="text-xs text-[#505050]">越南建设集团有限公司</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
      
      {/* 选项卡区域 */}
      <Tabs defaultValue="workflow" className="mt-6 px-2">
        <TabsList className="bg-background border border-[#E8E8E8]">
          <TabsTrigger value="workflow" className="data-[state=active]:bg-[#E8E8E8]/50">
            <Activity className="h-4 w-4 mr-2" />
            项目流程
          </TabsTrigger>
          <TabsTrigger value="notes" className="data-[state=active]:bg-[#E8E8E8]/50">
            <StickyNote className="h-4 w-4 mr-2" />
            备注记录
          </TabsTrigger>
          <TabsTrigger value="documents" className="data-[state=active]:bg-[#E8E8E8]/50">
            <FileText className="h-4 w-4 mr-2" />
            项目文档
          </TabsTrigger>
          <TabsTrigger value="contacts" className="data-[state=active]:bg-[#E8E8E8]/50">
            <Users className="h-4 w-4 mr-2" />
            联系人
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="workflow" className="mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* ReactFlow流程图 - 占2列 */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">项目流程图</CardTitle>
                  <CardDescription>点击流程图中的节点查看详细信息，包含所有分支决策</CardDescription>
                </CardHeader>
                <CardContent>
                  <ReactFlowWorkflow
                    onNodeClick={handleNodeClick}
                    activeNodeId={activeNodeId}
                    statusHistory={STATUS_HISTORY}
                  />
                </CardContent>
              </Card>
            </div>

            {/* 状态流转历史 - 占1列 */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">状态流转历史</CardTitle>
                      <CardDescription>项目各节点的状态变更记录</CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-600 mb-1">当前状态</div>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-blue-300">
                        <Activity className="w-3 h-3 mr-1" />
                        报价审批与出图阶段
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="max-h-[850px] overflow-y-auto">
                  <StatusHistoryList
                    statusHistory={STATUS_HISTORY}
                    onHistoryItemClick={handleHistoryItemClick}
                    activeHistoryId={activeHistoryId}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="notes" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">备注记录</CardTitle>
              <Button size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-1" />
                添加备注
              </Button>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-4">
                {notes.map((note, index) => (
                  <div key={index} className="border rounded-md p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center text-white text-xs font-medium">
                          {note.author[0]}
                        </div>
                        <span className="font-medium text-sm">{note.author}</span>
                      </div>
                      <time className="text-xs text-[#505050]">{note.time}</time>
                    </div>
                    <p className="text-sm">{note.content}</p>
                    {note.tags && (
                      <div className="flex items-center gap-2">
                        {note.tags.map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs border-[#E8E8E8] text-[#505050]">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="documents" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">项目文档</CardTitle>
              <Button size="sm" variant="outline">
                <Upload className="h-4 w-4 mr-1" />
                上传文档
              </Button>
            </CardHeader>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 gap-3">
                {documents.map((document, index) => (
                  <div key={index} className="flex items-center justify-between border rounded-md p-3">
                    <div className="flex items-center gap-3">
                      <div className={`h-9 w-9 rounded-md flex items-center justify-center ${
                        document.type === 'pdf' ? 'bg-[#F8BBD0]/50' : // Midea 浅粉
                        document.type === 'doc' ? 'bg-[#B2EBF2]/50' : // Midea 浅蓝
                        document.type === 'xls' ? 'bg-[#B2DFDB]/50' : // Midea 浅青
                        document.type === 'ppt' ? 'bg-[#FFECB3]/50' : // Midea 浅橙
                        'bg-[#E8E8E8]/50' // Midea 浅灰
                      }`}>
                        <FileText className={`h-5 w-5 ${
                          document.type === 'pdf' ? 'text-[#E91E63]' : // Midea 红
                          document.type === 'doc' ? 'text-[#0092D8]' : // Midea 主蓝
                          document.type === 'xls' ? 'text-[#00B4AA]' : // Midea 青
                          document.type === 'ppt' ? 'text-[#FF9800]' : // Midea 橙
                          'text-[#505050]' // Midea 深灰
                        }`} />
                      </div>
                      <div>
                        <p className="font-medium text-sm">{document.name}</p>
                        <div className="flex items-center gap-2 text-xs text-[#505050]">
                          <span>{document.size}</span>
                          <span>•</span>
                          <span>{document.uploadedAt}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button size="icon" variant="ghost" className="h-8 w-8">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button size="icon" variant="ghost" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="contacts" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">客户联系人</CardTitle>
              <Button size="sm" variant="outline">
                <UserPlus className="h-4 w-4 mr-1" />
                添加联系人
              </Button>
            </CardHeader>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {contacts.map((contact, index) => (
                  <div key={index} className="border rounded-md p-4 space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center text-white font-medium">
                        {contact.name[0]}
                      </div>
                      <div>
                        <p className="font-medium">{contact.name}</p>
                        <p className="text-sm text-[#505050]">{contact.position}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 gap-1">
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-3.5 w-3.5 text-[#505050]" />
                        <span>{contact.phone}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="h-3.5 w-3.5 text-[#505050]" />
                        <span>{contact.email}</span>
                      </div>
                      {contact.wechat && (
                        <div className="flex items-center gap-2 text-sm">
                          <MessageSquare className="h-3.5 w-3.5 text-[#505050]" />
                          <span>{contact.wechat}</span>
                        </div>
                      )}
                    </div>
                    <div className="pt-2 flex justify-end gap-2">
                      <Button size="sm" variant="ghost">
                        <Phone className="h-3.5 w-3.5 mr-1" />
                        电话
                      </Button>
                      <Button size="sm" variant="ghost">
                        <Mail className="h-3.5 w-3.5 mr-1" />
                        邮件
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  ) 
}