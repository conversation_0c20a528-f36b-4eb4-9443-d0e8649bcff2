@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem;
  /* 基础颜色 */
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  
  /* 主色调：美的蓝 Midea Blue */
  --primary: oklch(0.63 0.1459 240.63); /* #008CDA - 美的蓝 */
  --primary-foreground: oklch(1 0 0);

  /* 按钮颜色 */
  --button-primary: oklch(0.68 0.191 40.19);
  --button-primary-foreground: oklch(1 0 0);

  /* 次要颜色 */
  --secondary: oklch(0.95 0.03 250.0);
  --secondary-foreground: oklch(0.2 0.01 285.0);
  --muted: oklch(0.95 0.01 286.0);
  --muted-foreground: oklch(0.55 0.02 285.0);
  --accent: oklch(0.95 0.01 286.0);
  --accent-foreground: oklch(0.2 0.01 285.0);
  
  /* 强调色/功能色 */
  --destructive: oklch(0.65 0.27 30.0); /* 红色系警告色 */
  --border: oklch(0.9 0.01 286.0);
  --input: oklch(0.9 0.01 286.0);
  --ring: oklch(0.57 0.18 250.0); /* 与主色调相同 */
  
  /* 图表颜色 - 从手册提取的强调色 */
  --chart-1: oklch(0.65 0.18 160.0); /* 绿色 #00C6A9 */
  --chart-2: oklch(0.57 0.18 250.0); /* 蓝色 #008CDA */
  --chart-3: oklch(0.55 0.22 290.0); /* 紫色 #7653D9 */
  --chart-4: oklch(0.65 0.25 15.0);  /* 红色 #FF2D55 */
  --chart-5: oklch(0.70 0.20 40.0);  /* 橙色 #FF6B00 */
  
  /* 侧边栏颜色 */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.57 0.18 250.0); /* 与主色调相同 */
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.95 0.01 286.0);
  --sidebar-accent-foreground: oklch(0.2 0.01 285.0);
  --sidebar-border: oklch(0.9 0.01 286.0);
  --sidebar-ring: oklch(0.57 0.18 250.0); /* 与主色调相同 */
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  
  /* 暗色主题主色调：更亮的美的蓝 */
  --primary: oklch(0.62 0.22 250.0); /* 更亮的美的蓝 */
  --primary-foreground: oklch(0.2 0.04 250.0); /* 更暗的美的蓝 */
  
  /* 暗色主题次要颜色 */
  --secondary: oklch(0.27 0.01 286.0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.27 0.01 286.0);
  --muted-foreground: oklch(0.7 0.015 286.0);
  --accent: oklch(0.27 0.01 286.0);
  --accent-foreground: oklch(0.985 0 0);
  
  /* 暗色主题强调色/功能色 */
  --destructive: oklch(0.7 0.19 22.0); /* 暗色红色警告 */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.62 0.22 250.0); /* 与主色调相同 */
  
  /* 暗色主题图表颜色 - 与手册对应的暗色系 */
  --chart-1: oklch(0.68 0.22 250.0); /* 深蓝 - 主色调 */
  --chart-2: oklch(0.7 0.17 160.0); /* 深绿 */
  --chart-3: oklch(0.65 0.19 20.0);  /* 暗橙 */
  --chart-4: oklch(0.63 0.26 305.0); /* 深紫 */
  --chart-5: oklch(0.65 0.24 16.0);  /* 深红 */
  
  /* 暗色主题侧边栏颜色 */
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.62 0.22 250.0); /* 与主色调相同 */
  --sidebar-primary-foreground: oklch(0.2 0.04 250.0);
  --sidebar-accent: oklch(0.27 0.01 286.0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.62 0.22 250.0); /* 与主色调相同 */
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}


/* ---------- 通用类 ---------- */
.hover-scroll {
  /* ——— Firefox ——— */
  scrollbar-width: thin;                 /* 保留细滚动条 */
  scrollbar-color: transparent transparent;
  transition: scrollbar-color .7s ease; /* 让 Firefox 的 thumb 也淡入淡出 */
}

/* ——— Chrome / Edge / Safari ——— */
.hover-scroll::-webkit-scrollbar {
  width: 8px;                            /* 固定宽度，不动画 */
  height: 8px;
}
.hover-scroll::-webkit-scrollbar-track {
  background: transparent;               /* 轨道透明 */
}
.hover-scroll::-webkit-scrollbar-thumb {
  background: #888;
  opacity: 0;                            /* 初始透明 */
  border-radius: 4px;
  transition: opacity .25s ease;         /* Fading 关键 */
}

/* ---------- Hover 状态 ---------- */
.hover-scroll:hover {
  /* Firefox：thumb 颜色由透明 → 实色，轨道仍透明 */
  scrollbar-color: #888 transparent;
}
.hover-scroll:hover::-webkit-scrollbar-thumb {
  opacity: 1;                            /* WebKit：淡入 */
}

/* Override default input focus ring */
/* input:focus-visible {
  @apply outline-none ring-3;
} */

/* 强制显示ReactFlow边连接线 */
.react-flow__edge {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: all !important;
}

.react-flow__edge-path {
  stroke: #000000 !important;
  stroke-width: 3px !important;
  fill: none !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.react-flow__edge-text {
  fill: #000000 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  display: block !important;
  visibility: visible !important;
}

.react-flow__arrowhead {
  fill: #000000 !important;
  stroke: #000000 !important;
  display: block !important;
  visibility: visible !important;
}

svg.react-flow__edge {
  overflow: visible !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.react-flow__edges {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.react-flow__edge g {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 强制显示所有路径元素 */
path[class*="react-flow__edge"] {
  stroke: #000000 !important;
  stroke-width: 3px !important;
  fill: none !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 强制显示箭头标记 */
marker[id*="react-flow__arrowhead"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

marker[id*="react-flow__arrowhead"] path {
  fill: #000000 !important;
  stroke: #000000 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}
