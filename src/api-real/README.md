# HTTP 客户端使用指南

这个模块提供了一个基于 axios 的 HTTP 客户端，专门为项目的 API 调用需求而设计。

## 功能特性

- ✅ 基于 axios 的 HTTP 客户端封装
- ✅ 统一的请求/响应拦截器
- ✅ 标准化的 API 响应格式处理
- ✅ 自动错误处理和类型安全
- ✅ Token 认证支持（开发阶段已注释）
- ✅ TypeScript 完整类型支持
- ✅ 开发环境请求/响应日志

## API 响应格式

所有 API 接口返回统一格式：

```json
{
  "code": 0,           // 0: 成功, -1: 失败
  "msg": "操作成功",    // 响应消息
  "data": {...}        // 实际数据，失败时为 null
}
```

## 快速开始

### 1. 基本导入

```typescript
import { httpClient, get, post, put, delete as del, ApiError } from '@/api-real';
```

### 2. 使用快捷方法

```typescript
// GET 请求
const users = await get<User[]>('/users');

// POST 请求
const newUser = await post<User>('/users', {
  name: '<PERSON>',
  email: '<EMAIL>'
});

// PUT 请求
const updatedUser = await put<User>(`/users/${id}`, userData);

// DELETE 请求
await del(`/users/${id}`);
```

### 3. 使用 httpClient 实例

```typescript
// 带参数的 GET 请求
const users = await httpClient.get('/users', { page: 1, limit: 10 });

// 带自定义配置的请求
const result = await httpClient.post('/upload', formData, {
  headers: { 'Content-Type': 'multipart/form-data' },
  timeout: 120000
});
```

## 错误处理

```typescript
try {
  const data = await get('/api/data');
} catch (error) {
  if (error instanceof ApiError) {
    console.error('API 错误:', error.code, error.msg);
    // 根据错误码处理不同情况
    if (error.code === -1) {
      // 业务逻辑错误
    }
  } else {
    console.error('网络或其他错误:', error);
  }
}
```

## 认证配置

### 开发阶段
当前为开发阶段，token 认证逻辑已被注释。所有请求都会跳过认证。

### 生产环境
要启用 token 认证，请在 `request.ts` 中取消注释以下代码：

```typescript
// 在 setupRequestInterceptor 方法中
const token = localStorage.getItem('token');
if (token) {
  config.headers.Authorization = `Bearer ${token}`;
}
```

## 自定义配置

### 请求配置选项

```typescript
interface RequestConfig {
  skipAuth?: boolean;        // 跳过认证
  timeout?: number;          // 自定义超时时间
  headers?: Record<string, string>; // 额外请求头
}
```

### 使用示例

```typescript
// 跳过认证的请求
await get('/public/data', {}, { skipAuth: true });

// 自定义超时时间
await post('/upload', data, { timeout: 300000 });

// 自定义请求头
await get('/api/data', {}, {
  headers: { 'X-Custom-Header': 'value' }
});
```

## API 类封装示例

```typescript
export class UserApi {
  static async getUsers(page = 1, limit = 10): Promise<User[]> {
    try {
      return await get<User[]>('/users', { page, limit });
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`获取用户列表失败: ${error.msg}`);
      }
      throw error;
    }
  }

  static async createUser(userData: CreateUserRequest): Promise<User> {
    return await post<User>('/users', userData);
  }
}
```

## 环境配置

在 `.env.local` 文件中设置 API 基础 URL：

```env
NEXT_PUBLIC_API_URL=https://your-api-domain.com/api
```

## 开发调试

开发环境下，所有请求和响应都会在控制台打印日志：

```
📤 API 请求: { method: 'GET', url: '/users', data: null }
📥 API 响应: { url: '/users', status: 200, data: [...] }
```

## 注意事项

1. **登录接口**: `/login` 路径的请求会自动跳过 token 验证
2. **错误处理**: 所有 HTTP 错误都会被转换为 `ApiError` 实例
3. **响应数据**: 成功的请求只返回 `data` 字段，不包含 `code` 和 `msg`
4. **Token 过期**: 401 错误会自动清除本地 token 并跳转到登录页

## 文件结构

```
src/api-real/
├── index.ts          # 统一导出
├── request.ts        # HTTP 客户端主文件
├── types.ts          # 类型定义
├── example.ts        # 使用示例
└── README.md         # 使用文档
```
