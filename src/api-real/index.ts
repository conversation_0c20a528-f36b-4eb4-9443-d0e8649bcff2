/**
 * API 模块统一导出
 */

// 导出 HTTP 客户端
export { default as httpClient, get, post, put, del as delete, patch } from './request';

// 导出类型定义
export type { ApiResponse, RequestConfig } from './types';
export { ApiError } from './types';

// 使用示例：
// import { httpClient, get, post, ApiError } from '@/api-real';
// 
// // 方式1：使用 httpClient 实例
// const data = await httpClient.get('/users');
// 
// // 方式2：使用快捷方法
// const users = await get('/users');
// const newUser = await post('/users', { name: 'John' });
// 
// // 错误处理
// try {
//   const result = await get('/api/data');
// } catch (error) {
//   if (error instanceof ApiError) {
//     console.error('API 错误:', error.msg);
//   }
// }
