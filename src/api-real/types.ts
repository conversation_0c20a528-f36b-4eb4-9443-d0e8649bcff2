/**
 * API 响应的标准格式
 */
export interface ApiResponse<T = any> {
  /** 响应状态码：0 表示成功，-1 表示失败 */
  code: 0 | -1;
  /** 响应消息 */
  msg: string;
  /** 响应数据，成功时包含实际数据，失败时为 null */
  data: T | null;
}

/**
 * API 错误类
 */
export class ApiError extends Error {
  public code: number;
  public msg: string;

  constructor(code: number, msg: string) {
    super(msg);
    this.name = 'ApiError';
    this.code = code;
    this.msg = msg;
  }
}

/**
 * 请求配置接口
 */
export interface RequestConfig {
  /** 是否跳过 token 验证 */
  skipAuth?: boolean;
  /** 自定义超时时间 */
  timeout?: number;
  /** 额外的请求头 */
  headers?: Record<string, string>;
}
