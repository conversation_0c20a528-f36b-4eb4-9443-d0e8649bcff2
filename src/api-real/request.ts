import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, ApiError, RequestConfig } from './types';

/**
 * HTTP 客户端类
 * 封装 axios，提供统一的请求和响应处理
 */
class HttpClient {
  private instance: AxiosInstance;

  constructor() {
    // 创建 axios 实例
    this.instance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://api.example.com',
      timeout: 90000, // 90秒超时
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();
    
    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.instance.interceptors.request.use(
      (config) => {
        // 排除 login 接口和标记为跳过认证的请求
        const skipAuth = config.url?.includes('/login') || 
                        (config as any).skipAuth === true;

        if (!skipAuth) {
          // 开发阶段：注释掉 token 认证逻辑
          // const token = localStorage.getItem('token');
          // if (token) {
          //   config.headers.Authorization = `Bearer ${token}`;
          // }
          
          // 生产环境时取消注释上面的代码
          console.log('🔧 开发模式：跳过 token 认证');
        }

        // 打印请求信息（开发环境）
        if (process.env.NODE_ENV === 'development') {
          console.log('📤 API 请求:', {
            method: config.method?.toUpperCase(),
            url: config.url,
            data: config.data,
            params: config.params,
          });
        }

        return config;
      },
      (error) => {
        console.error('❌ 请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // 打印响应信息（开发环境）
        if (process.env.NODE_ENV === 'development') {
          console.log('📥 API 响应:', {
            url: response.config.url,
            status: response.status,
            data: response.data,
          });
        }

        const { code, msg, data } = response.data;

        // 根据业务状态码处理响应
        if (code === 0) {
          // 请求成功，返回 data 字段
          return data;
        } else if (code === -1) {
          // 请求失败，抛出业务错误
          throw new ApiError(code, msg || '请求失败');
        } else {
          // 未知状态码
          throw new ApiError(code, msg || '未知错误');
        }
      },
      (error) => {
        console.error('❌ 响应拦截器错误:', error);

        // 处理 HTTP 状态码错误
        if (error.response) {
          const { status, statusText } = error.response;
          
          switch (status) {
            case 401:
              // 未授权，清除 token 并跳转到登录页
              localStorage.removeItem('token');
              if (typeof window !== 'undefined') {
                window.location.href = '/login';
              }
              throw new ApiError(401, '登录已过期，请重新登录');
              
            case 403:
              throw new ApiError(403, '没有权限访问该资源');
              
            case 404:
              throw new ApiError(404, '请求的资源不存在');
              
            case 500:
              throw new ApiError(500, '服务器内部错误');
              
            case 502:
            case 503:
            case 504:
              throw new ApiError(status, '服务暂时不可用，请稍后重试');
              
            default:
              throw new ApiError(status, statusText || '网络请求失败');
          }
        } else if (error.request) {
          // 网络错误
          throw new ApiError(0, '网络连接失败，请检查网络设置');
        } else {
          // 其他错误
          throw new ApiError(0, error.message || '请求配置错误');
        }
      }
    );
  }

  /**
   * GET 请求
   */
  public get<T = any>(
    url: string, 
    params?: any, 
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.get(url, {
      params,
      ...config,
    });
  }

  /**
   * POST 请求
   */
  public post<T = any>(
    url: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.post(url, data, config);
  }

  /**
   * PUT 请求
   */
  public put<T = any>(
    url: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.put(url, data, config);
  }

  /**
   * DELETE 请求
   */
  public delete<T = any>(
    url: string, 
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.delete(url, config);
  }

  /**
   * PATCH 请求
   */
  public patch<T = any>(
    url: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.patch(url, data, config);
  }

  /**
   * 获取原始 axios 实例（用于特殊需求）
   */
  public getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 创建并导出 HTTP 客户端实例
const httpClient = new HttpClient();

export default httpClient;

// 导出常用方法的快捷方式
export const { get, post, put, delete: del, patch } = httpClient;
