/**
 * HTTP 客户端使用示例
 * 这个文件展示了如何使用封装的 HTTP 客户端进行 API 调用
 */

import { httpClient, get, post, put, delete as del, ApiError } from './index';

// 定义用户数据类型
interface User {
  id: number;
  name: string;
  email: string;
}

interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  token: string;
  user: User;
}

/**
 * 用户相关 API 示例
 */
export class UserApi {
  /**
   * 用户登录
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // 登录接口会自动跳过 token 验证
      const result = await post<LoginResponse>('/auth/login', credentials);
      
      // 登录成功后保存 token（生产环境使用）
      // if (result.token) {
      //   localStorage.setItem('token', result.token);
      // }
      
      return result;
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`登录失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 获取用户列表
   */
  static async getUsers(page = 1, limit = 10): Promise<User[]> {
    try {
      return await get<User[]>('/users', { page, limit });
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`获取用户列表失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 获取单个用户
   */
  static async getUser(id: number): Promise<User> {
    try {
      return await get<User>(`/users/${id}`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`获取用户信息失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 创建用户
   */
  static async createUser(userData: Omit<User, 'id'>): Promise<User> {
    try {
      return await post<User>('/users', userData);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`创建用户失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 更新用户
   */
  static async updateUser(id: number, userData: Partial<User>): Promise<User> {
    try {
      return await put<User>(`/users/${id}`, userData);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`更新用户失败: ${error.msg}`);
      }
      throw error;
    }
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: number): Promise<void> {
    try {
      await del<void>(`/users/${id}`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new Error(`删除用户失败: ${error.msg}`);
      }
      throw error;
    }
  }
}

/**
 * 在 React 组件中的使用示例
 */
export const useUserApi = () => {
  const handleLogin = async (username: string, password: string) => {
    try {
      const result = await UserApi.login({ username, password });
      console.log('登录成功:', result);
      return result;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  const handleGetUsers = async () => {
    try {
      const users = await UserApi.getUsers();
      console.log('用户列表:', users);
      return users;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  };

  return {
    handleLogin,
    handleGetUsers,
  };
};

/**
 * 直接使用 httpClient 的示例
 */
export const directApiCall = async () => {
  try {
    // 使用 httpClient 实例
    const data = await httpClient.get('/api/data');
    console.log('获取数据成功:', data);

    // 使用自定义配置
    const result = await httpClient.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 120000, // 2分钟超时
    });
    console.log('上传成功:', result);

  } catch (error) {
    if (error instanceof ApiError) {
      console.error('API 错误:', error.code, error.msg);
    } else {
      console.error('其他错误:', error);
    }
  }
};
