"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { StaffLog, OperationType, SecurityLevel } from '@/api/staff/types'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  User,
  Building,
  Briefcase,
  MapPin,
  Activity,
  Shield,
  Clock,
  Globe,
  Monitor,
  FileText,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { toast } from 'sonner'

interface StaffLogDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  log?: StaffLog
}

// 操作类型配置
const operationTypeConfig = {
  [OperationType.READ]: { 
    label: '读取操作', 
    icon: Eye, 
    className: 'bg-blue-100 text-blue-800 border-blue-200'
  },
  [OperationType.WRITE]: { 
    label: '写入操作', 
    icon: FileText, 
    className: 'bg-orange-100 text-orange-800 border-orange-200'
  }
}

// 安全等级配置
const securityLevelConfig = {
  [SecurityLevel.LOW]: { 
    label: '低危险', 
    className: 'bg-green-100 text-green-800 border-green-200',
    description: '日常操作，风险较低'
  },
  [SecurityLevel.MEDIUM]: { 
    label: '中危险', 
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    description: '需要关注的操作，存在一定风险'
  },
  [SecurityLevel.HIGH]: { 
    label: '高危险', 
    className: 'bg-red-100 text-red-800 border-red-200',
    description: '高风险操作，需要特别监控'
  }
}

export function StaffLogDetailDialog({
  open,
  onOpenChange,
  log,
}: StaffLogDetailDialogProps) {
  const [copied, setCopied] = useState(false)

  if (!log) return null

  const formatDateTime = (dateTimeString: string) => {
    try {
      return format(new Date(dateTimeString), 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })
    } catch {
      return dateTimeString
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      toast.success('已复制到剪贴板')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('复制失败')
    }
  }

  const operationConfig = operationTypeConfig[log.operation_type]
  const securityConfig = securityLevelConfig[log.security_level]
  const OperationIcon = operationConfig.icon

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            员工操作日志详情
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <User className="h-4 w-4" />
                操作员工信息
              </h3>
              <Badge
                variant="outline"
                className="font-mono cursor-pointer hover:bg-accent"
                onClick={() => copyToClipboard(log.id)}
              >
                {log.id}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <User className="h-3 w-3" />
                  员工姓名
                </label>
                <p className="text-base font-medium">{log.staff_name}</p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">员工编号</label>
                <p className="text-base font-mono text-sm">{log.staff_id}</p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  所属部门
                </label>
                <Badge variant="outline">{log.staff_department}</Badge>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Briefcase className="h-3 w-3" />
                  职位
                </label>
                <p className="text-base">{log.staff_position}</p>
              </div>
              
              {log.staff_region && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    所在区域
                  </label>
                  <p className="text-base">{log.staff_region}</p>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* 操作信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-4 w-4" />
              操作详情
            </h3>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">操作内容</label>
                <p className="text-base font-medium">{log.operation_content}</p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">详细描述</label>
                <div className="p-3 bg-muted/50 rounded-lg">
                  <p className="text-sm leading-relaxed">{log.operation_details}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">操作类型</label>
                  <Badge className={`${operationConfig.className} flex items-center gap-1 w-fit`}>
                    <OperationIcon className="h-3 w-3" />
                    {operationConfig.label}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    安全等级
                  </label>
                  <div className="space-y-1">
                    <Badge className={`${securityConfig.className} flex items-center gap-1 w-fit`}>
                      <Shield className="h-3 w-3" />
                      {securityConfig.label}
                    </Badge>
                    <p className="text-xs text-muted-foreground">{securityConfig.description}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* 系统信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              系统信息
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  操作时间
                </label>
                <p className="text-base">{formatDateTime(log.created_at)}</p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">记录来源</label>
                <Badge variant="secondary">{log.created_by === 'system' ? '系统记录' : '管理员记录'}</Badge>
              </div>
              
              {log.ip_address && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Globe className="h-3 w-3" />
                    IP地址
                  </label>
                  <p className="text-base font-mono text-sm">{log.ip_address}</p>
                </div>
              )}
              
              {log.user_agent && (
                <div className="space-y-2 md:col-span-2">
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Monitor className="h-3 w-3" />
                    用户代理
                  </label>
                  <div className="p-2 bg-muted/50 rounded text-xs font-mono break-all">
                    {log.user_agent}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button
            variant="outline"
            onClick={() => copyToClipboard(JSON.stringify(log, null, 2))}
          >
            {copied ? '已复制' : '复制完整信息'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 