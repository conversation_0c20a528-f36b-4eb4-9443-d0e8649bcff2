"use client"

import { useCallback, useEffect, useState } from 'react'
import { StaffLogsTable } from './StaffLogsTable'
import { StaffLogsSearch } from './StaffLogsSearch'
import { StaffLogsFilter } from './StaffLogsFilter'
import { StaffLogDetailDialog } from './StaffLogDetailDialog'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'
import { useDebounce } from '@/hooks/use-debounce'
import { StaffLog } from '@/api/staff/types'
import { getMockPaginatedStaffLogs } from '@/lib/mock-staff-logs-data'

export function StaffLogsList() {
  const [logs, setLogs] = useState<StaffLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  
  // 筛选状态
  const [operationType, setOperationType] = useState<string>('all')
  const [securityLevel, setSecurityLevel] = useState<string>('all')
  const [department, setDepartment] = useState<string>('all')
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize] = useState(16)
  
  // 详情对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [detailLog, setDetailLog] = useState<StaffLog | undefined>(undefined)
  
  // 获取分页日志列表
  const fetchPaginatedLogs = useCallback(async (
    page: number, 
    search: string,
    opType: string,
    secLevel: string,
    dept: string
  ) => {
    setIsLoading(true)
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const mockResponse = getMockPaginatedStaffLogs(
        page, 
        pageSize, 
        search,
        opType,
        secLevel,
        dept
      )
      setLogs(mockResponse.logs)
      setTotalPages(mockResponse.total_pages)
    } catch (error) {
      console.error("获取员工日志错误:", error)
      toast.error("获取员工日志失败，请稍后重试")
      setLogs([])
      setTotalPages(1)
    } finally {
      setIsLoading(false)
    }
  }, [pageSize])
  
  // 当筛选条件变化时重新加载数据
  useEffect(() => {
    fetchPaginatedLogs(
      currentPage, 
      debouncedSearchTerm,
      operationType,
      securityLevel,
      department
    )
  }, [
    currentPage, 
    debouncedSearchTerm, 
    operationType,
    securityLevel,
    department,
    fetchPaginatedLogs
  ])
  
  // 处理搜索输入变化
  const handleSearchInputChange = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1)
  }
  
  // 处理筛选变化
  const handleFilterChange = (filters: {
    operationType?: string;
    securityLevel?: string;
    department?: string;
  }) => {
    if (filters.operationType !== undefined) {
      setOperationType(filters.operationType)
    }
    if (filters.securityLevel !== undefined) {
      setSecurityLevel(filters.securityLevel)
    }
    if (filters.department !== undefined) {
      setDepartment(filters.department)
    }
    setCurrentPage(1)
  }
  
  // 处理页面变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  // 打开日志详情对话框
  const handleViewClick = (log: StaffLog) => {
    setDetailLog(log)
    setDetailDialogOpen(true)
  }

  // 重置筛选条件
  const handleResetFilters = () => {
    setOperationType('all')
    setSecurityLevel('all')
    setDepartment('all')
    setSearchTerm('')
    setCurrentPage(1)
  }

  // 显示全页面加载状态
  if (isLoading && logs.length === 0 && !debouncedSearchTerm && operationType === 'all' && securityLevel === 'all' && department === 'all') {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">正在加载员工日志数据...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-4">
        <StaffLogsSearch 
          onSearch={handleSearchInputChange} 
          onResetFilters={handleResetFilters}
        />
        
        <StaffLogsFilter
          operationType={operationType}
          securityLevel={securityLevel}
          department={department}
          onFilterChange={handleFilterChange}
        />
        
        <StaffLogsTable 
          logs={logs} 
          isLoading={isLoading && logs.length === 0} 
          onView={handleViewClick}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </div>

      <StaffLogDetailDialog
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        log={detailLog}
      />
    </>
  )
} 