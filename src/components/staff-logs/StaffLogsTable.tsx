"use client"

import { useState } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Eye, Copy, Check } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import { useIsMobile } from '@/hooks/use-mobile'
import { toast } from 'sonner'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { PaginationControls } from '@/components/share/pagination'
import { StaffLog, OperationType, SecurityLevel } from '@/api/staff/types'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 截断字符串的工具函数
const truncateString = (str: string, maxLength: number) => {
  if (!str) return '';
  return str.length > maxLength ? str.substring(0, maxLength) + '...' : str;
}

// 复制按钮组件
function CopyButton({ text }: { text: string }) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      toast.success('已复制到剪贴板')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('复制失败')
    }
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="h-3 w-3 text-green-500" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{copied ? '已复制' : '复制'}</p>
      </TooltipContent>
    </Tooltip>
  )
}

// 操作类型Badge
function OperationTypeBadge({ type }: { type: OperationType }) {
  const config = {
    [OperationType.READ]: { label: '读取', variant: 'outline' as const },
    [OperationType.WRITE]: { label: '写入', variant: 'default' as const }
  }
  
  const { label, variant } = config[type]
  
  return (
    <Badge variant={variant} className="text-xs">
      {label}
    </Badge>
  )
}

// 安全等级Badge
function SecurityLevelBadge({ level }: { level: SecurityLevel }) {
  const config = {
    [SecurityLevel.LOW]: { label: '低危险', className: 'bg-green-500 text-white' },
    [SecurityLevel.MEDIUM]: { label: '中危险', className: 'bg-yellow-500 text-white' },
    [SecurityLevel.HIGH]: { label: '高危险', className: 'bg-red-500 text-white' }
  }
  
  const { label, className } = config[level]
  
  return (
    <Badge className={`text-xs ${className}`}>
      {label}
    </Badge>
  )
}

// 格式化时间
function formatDateTime(dateTimeString: string) {
  try {
    return format(new Date(dateTimeString), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
  } catch {
    return dateTimeString
  }
}

interface StaffLogsTableProps {
  logs: StaffLog[]
  isLoading: boolean
  onView: (log: StaffLog) => void
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}

export function StaffLogsTable({
  logs,
  isLoading,
  onView,
  currentPage,
  totalPages,
  onPageChange,
}: StaffLogsTableProps) {
  const isMobile = useIsMobile()

  if (isLoading) {
    return (
      <div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>日志ID</TableHead>
              <TableHead>员工</TableHead>
              {!isMobile && (
                <>
                  <TableHead>部门</TableHead>
                  <TableHead>职位</TableHead>
                  <TableHead>区域</TableHead>
                  <TableHead>操作内容</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>安全等级</TableHead>
                  <TableHead>时间</TableHead>
                </>
              )}
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                {!isMobile && (
                  <>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  </>
                )}
                <TableCell className="text-right">
                  <Skeleton className="h-8 w-16 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-4">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[12%]">日志ID</TableHead>
                <TableHead className="w-[10%]">员工</TableHead>
                {!isMobile && (
                  <>
                    <TableHead className="w-[8%]">部门</TableHead>
                    <TableHead className="w-[10%]">职位</TableHead>
                    <TableHead className="w-[8%]">区域</TableHead>
                    <TableHead className="w-[15%]">操作内容</TableHead>
                    <TableHead className="w-[8%]">类型</TableHead>
                    <TableHead className="w-[10%]">安全等级</TableHead>
                    <TableHead className="w-[14%]">时间</TableHead>
                  </>
                )}
                <TableHead className="w-[5%] text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isMobile ? 3 : 10} className="text-center py-8 text-muted-foreground">
                    暂无日志数据
                  </TableCell>
                </TableRow>
              ) : (
                logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="font-mono text-xs">
                      <div className="flex items-center gap-1">
                        <span>{truncateString(log.id, 10)}</span>
                        <CopyButton text={log.id} />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{log.staff_name}</span>
                        <span className="text-xs text-muted-foreground font-mono">
                          {log.staff_id}
                        </span>
                      </div>
                    </TableCell>
                    {!isMobile && (
                      <>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {log.staff_department}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {log.staff_position}
                        </TableCell>
                        <TableCell className="text-sm">
                          {log.staff_region || '-'}
                        </TableCell>
                        <TableCell>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-help">
                                {truncateString(log.operation_content, 20)}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs">{log.operation_content}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TableCell>
                        <TableCell>
                          <OperationTypeBadge type={log.operation_type} />
                        </TableCell>
                        <TableCell>
                          <SecurityLevelBadge level={log.security_level} />
                        </TableCell>
                        <TableCell className="text-sm">
                          {formatDateTime(log.created_at)}
                        </TableCell>
                      </>
                    )}
                    <TableCell className="text-center">
                      <div className="flex justify-center">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onView(log)}
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>查看详情</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        
        {totalPages > 1 && (
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
          />
        )}
      </div>
    </TooltipProvider>
  )
} 