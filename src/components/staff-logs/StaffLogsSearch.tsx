"use client"

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { RotateCcw, Search } from 'lucide-react'

interface StaffLogsSearchProps {
  onSearch: (searchTerm: string) => void
  onResetFilters: () => void
}

export function StaffLogsSearch({ onSearch, onResetFilters }: StaffLogsSearchProps) {
  const [searchTerm, setSearchTerm] = useState('')

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    onSearch(value)
  }

  const handleReset = () => {
    setSearchTerm('')
    onResetFilters()
  }

  return (
    <div className="flex flex-col sm:flex-row gap-4 mb-6 w-full">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索员工姓名、操作内容、操作详情、部门、职位、区域..."
          value={searchTerm}
          onChange={handleSearch}
          className="pl-9 w-full"
        />
      </div>
      <Button onClick={handleReset} variant="outline" className="flex-shrink-0">
        <RotateCcw className="mr-2 h-4 w-4" />
        重置筛选
      </Button>
    </div>
  )
} 