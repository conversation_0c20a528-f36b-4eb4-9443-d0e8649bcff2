"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { OperationType, SecurityLevel } from '@/api/staff/types'
import { OperationTypeMap, SecurityLevelMap } from '@/api/enum'
import { Badge } from '@/components/ui/badge'
import { Filter } from 'lucide-react'

interface StaffLogsFilterProps {
  operationType: string
  securityLevel: string
  department: string
  onFilterChange: (filters: {
    operationType?: string;
    securityLevel?: string;
    department?: string;
  }) => void
}

const departments = [
  '技术部', '销售部', '财务部', '人事部', '市场部', '运营部', '客服部'
]

export function StaffLogsFilter({
  operationType,
  securityLevel,
  department,
  onFilterChange
}: StaffLogsFilterProps) {
  
  // 计算当前筛选条件数量
  const getActiveFiltersCount = () => {
    let count = 0
    if (operationType !== 'all') count++
    if (securityLevel !== 'all') count++
    if (department !== 'all') count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className="flex flex-col sm:flex-row gap-4 p-4 bg-muted/50 rounded-lg">
      <div className="flex items-center gap-2 flex-shrink-0">
        <Filter className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium">筛选条件</span>
        {activeFiltersCount > 0 && (
          <Badge variant="secondary" className="text-xs">
            {activeFiltersCount}
          </Badge>
        )}
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 flex-1">
        <div className="space-y-1 flex-1">
          <label className="text-xs text-muted-foreground">操作类型</label>
          <Select
            value={operationType}
            onValueChange={(value) => onFilterChange({ operationType: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder="选择操作类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value={OperationType.READ}>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">读</Badge>
                  {OperationTypeMap[OperationType.READ]}
                </div>
              </SelectItem>
              <SelectItem value={OperationType.WRITE}>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">写</Badge>
                  {OperationTypeMap[OperationType.WRITE]}
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1 flex-1">
          <label className="text-xs text-muted-foreground">安全等级</label>
          <Select
            value={securityLevel}
            onValueChange={(value) => onFilterChange({ securityLevel: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder="选择安全等级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部等级</SelectItem>
              <SelectItem value={SecurityLevel.LOW}>
                <div className="flex items-center gap-2">
                  <Badge variant="default" className="text-xs bg-green-500">低</Badge>
                  {SecurityLevelMap[SecurityLevel.LOW]}
                </div>
              </SelectItem>
              <SelectItem value={SecurityLevel.MEDIUM}>
                <div className="flex items-center gap-2">
                  <Badge variant="default" className="text-xs bg-yellow-500">中</Badge>
                  {SecurityLevelMap[SecurityLevel.MEDIUM]}
                </div>
              </SelectItem>
              <SelectItem value={SecurityLevel.HIGH}>
                <div className="flex items-center gap-2">
                  <Badge variant="default" className="text-xs bg-red-500">高</Badge>
                  {SecurityLevelMap[SecurityLevel.HIGH]}
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-1 flex-1">
          <label className="text-xs text-muted-foreground">部门</label>
          <Select
            value={department}
            onValueChange={(value) => onFilterChange({ department: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue placeholder="选择部门" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部部门</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept} value={dept}>
                  {dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
} 