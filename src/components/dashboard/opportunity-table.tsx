"use client";

import { useState, useMemo } from "react";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Calculator,
  Edit,
  MoreHorizontal,
  ReceiptText,
  Download,
  Filter,
  Search,
  Calendar,
  CalendarDays,
  ArrowRight,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { isElevatorSelectionSubmitted } from "@/lib/elevator-selection";

// 导入接口
export interface OpportunityData {
  id: string;
  projectCode: string;
  projectName: string;
  customerName: string;
  projectManagerName: string;
  salesName: string;
  salesAssistantName?: string;
  progress: number;
  status: string;
  createdAt: string;
  expectedSignDate: string;
  estimatedAmount: string;
  elevatorCount: string;
  projectTypeName: string;
  customerTypeName?: string;
  locationName?: string;
  projectAddress?: string;
  engineeringTypeName?: string;
  industryTypeName?: string;
  isLargeProjectName?: string;
  contractCode?: string;
  contactPhone?: string;
  contactEmail?: string;
  buttonGroups?: ButtonGroups;
  successProbability?: number;
  projectStatus?: string;
  statusUpdatedAt?: string;
}

export interface ButtonGroups {
  detail: boolean;
  submit: boolean;
  contract: boolean;
  hideBlueprint: boolean;
  hideElevator: boolean;
}

interface OpportunityTableProps {
  data: OpportunityData[];
  onUpdate: () => void;
  categoryName?: string;
}

// 项目状态样式 - 根据业务流程更新
const projectStatusStyles = {
  正询价: (
    <Badge variant="outline" className="bg-[#E3F2FD]/50 text-[#1976D2] border-[#E3F2FD] text-xs">
      正询价 (Inquiry)
    </Badge>
  ),
  已报价: (
    <Badge variant="outline" className="bg-[#F3E5F5]/50 text-[#7B1FA2] border-[#F3E5F5] text-xs">
      已报价 (Quotation)
    </Badge>
  ),
  已出图: (
    <Badge variant="outline" className="bg-[#E8F5E8]/50 text-[#388E3C] border-[#E8F5E8] text-xs">
      已出图 (Designed)
    </Badge>
  ),
  需求变更: (
    <Badge variant="outline" className="bg-[#FFF3E0]/50 text-[#F57C00] border-[#FFF3E0] text-xs">
      需求变更 (Change requirement)
    </Badge>
  ),
  已签约: (
    <Badge variant="outline" className="bg-[#E0F2F1]/50 text-[#00695C] border-[#E0F2F1] text-xs">
      已签约 (Contract signed)
    </Badge>
  ),
  已首付: (
    <Badge variant="outline" className="bg-[#E1F5FE]/50 text-[#0277BD] border-[#E1F5FE] text-xs">
      已首付 (Down payment)
    </Badge>
  ),
  图纸确认: (
    <Badge variant="outline" className="bg-[#F8BBD0]/50 text-[#E91E63] border-[#F8BBD0] text-xs">
      图纸确认 (GAD confirmed)
    </Badge>
  ),
  已排产: (
    <Badge variant="outline" className="bg-[#FFECB3]/50 text-[#FF9800] border-[#FFECB3] text-xs">
      已排产 (Production)
    </Badge>
  ),
  已发货: (
    <Badge variant="outline" className="bg-[#B2DFDB]/50 text-[#00B4AA] border-[#B2DFDB] text-xs">
      已发货 (Delivery)
    </Badge>
  ),
  已失失: (
    <Badge variant="outline" className="bg-[#FFCDD2]/50 text-[#D32F2F] border-[#FFCDD2] text-xs">
      已失失 (Lost)
    </Badge>
  ),
};

// 成功几率颜色样式
const getSuccessProbabilityStyle = (probability: number) => {
  if (probability >= 80) {
    return "text-[#2E7D32] font-semibold"; // 深绿色 - 优秀
  } else if (probability >= 60) {
    return "text-[#388E3C] font-semibold"; // 中绿色 - 良好
  } else if (probability >= 40) {
    return "text-[#F57C00] font-semibold"; // 橙色 - 一般
  } else {
    return "text-[#D32F2F] font-semibold"; // 红色 - 较低
  }
};

export function OpportunityTable({ data, onUpdate, categoryName = "电梯" }: OpportunityTableProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [followerFilter, setFollowerFilter] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  
  // 金额范围过滤
  const [minAmount, setMinAmount] = useState<string>("");
  const [maxAmount, setMaxAmount] = useState<string>("");
  
  // 创建日期范围过滤
  const [startCreatedDate, setStartCreatedDate] = useState<string>("");
  const [endCreatedDate, setEndCreatedDate] = useState<string>("");
  
  // 状态更新日期范围过滤
  const [startUpdatedDate, setStartUpdatedDate] = useState<string>("");
  const [endUpdatedDate, setEndUpdatedDate] = useState<string>("");

  // 过滤数据
  const filteredData = useMemo(() => {
    return data.filter((item) => {
      const matchesSearch = 
        item.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.projectCode.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === "all" || item.projectStatus === statusFilter;
      
      // 金额范围过滤
      const amount = parseFloat(item.estimatedAmount);
      const matchesAmount = 
        (minAmount === "" || amount >= parseFloat(minAmount)) &&
        (maxAmount === "" || amount <= parseFloat(maxAmount));
      
      // 创建日期范围过滤
      const createdDate = new Date(item.createdAt);
      const matchesCreatedDate = 
        (startCreatedDate === "" || createdDate >= new Date(startCreatedDate)) &&
        (endCreatedDate === "" || createdDate <= new Date(endCreatedDate + "T23:59:59"));
      
      // 状态更新日期范围过滤
      const updatedDate = new Date(item.statusUpdatedAt || item.createdAt);
      const matchesUpdatedDate = 
        (startUpdatedDate === "" || updatedDate >= new Date(startUpdatedDate)) &&
        (endUpdatedDate === "" || updatedDate <= new Date(endUpdatedDate + "T23:59:59"));

      const follower = item.salesName;
      const matchesFollower = followerFilter === "all" || follower.includes(followerFilter);

      return matchesSearch && matchesStatus && matchesAmount && matchesCreatedDate && matchesUpdatedDate && matchesFollower;
    });
  }, [data, searchTerm, statusFilter, minAmount, maxAmount, startCreatedDate, endCreatedDate, startUpdatedDate, endUpdatedDate, followerFilter]);

  // 获取所有跟进人选项
  const followerOptions = useMemo(() => {
    const followers = data.map(item => item.salesName);
    return Array.from(new Set(followers));
  }, [data]);

  // 处理操作
  const handleGoToSelection = (opportunityId: string) => {
    router.push(`/dashboard/opportunity/${opportunityId}/elevator-selection`);
  };

  const handleGoToQuotation = (opportunityId: string) => {
    router.push(`/dashboard/opportunity/${opportunityId}/quotation`);
  };

  const handleViewDetails = (opportunityId: string) => {
    router.push(`/dashboard/opportunity/${opportunityId}`);
  };

  const handleViewContract = () => {
    toast.info("合同功能开发中...");
  };

  const handleGenerateDrawing = () => {
    toast.info("图纸生成功能开发中...");
  };

  // 清空所有过滤器
  const handleClearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setFollowerFilter("all");
    setMinAmount("");
    setMaxAmount("");
    setStartCreatedDate("");
    setEndCreatedDate("");
    setStartUpdatedDate("");
    setEndUpdatedDate("");
  };

  return (
    <div className="space-y-4">
      {/* 搜索和过滤器 */}
      <div className="flex flex-col gap-4 p-4 bg-white rounded-lg shadow-sm">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="搜索项目名称、客户或项目号..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            筛选
          </Button>
          {showFilters && (
            <Button
              variant="ghost"
              onClick={handleClearFilters}
              className="text-gray-500 hover:text-gray-700"
            >
              清空筛选
            </Button>
          )}
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t">
            <div>
              <label className="text-sm font-medium mb-2 block">项目状态</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="正询价">正询价 (Inquiry)</SelectItem>
                  <SelectItem value="已报价">已报价 (Quotation)</SelectItem>
                  <SelectItem value="已出图">已出图 (Designed)</SelectItem>
                  <SelectItem value="需求变更">需求变更 (Change requirement)</SelectItem>
                  <SelectItem value="已签约">已签约 (Contract signed)</SelectItem>
                  <SelectItem value="已首付">已首付 (Down payment)</SelectItem>
                  <SelectItem value="图纸确认">图纸确认 (GAD confirmed)</SelectItem>
                  <SelectItem value="已排产">已排产 (Production)</SelectItem>
                  <SelectItem value="已发货">已发货 (Delivery)</SelectItem>
                  <SelectItem value="已失失">已失失 (Lost)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                <Filter className="h-4 w-4 text-[#00B4AA]" />
                投标金额范围 (万元)
              </label>
              <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50/50">
                <div className="flex-1">
                  <Input
                    type="number"
                    placeholder="最小金额"
                    value={minAmount}
                    onChange={(e) => setMinAmount(e.target.value)}
                    className="border-0 bg-white shadow-sm"
                  />
                </div>
                <div className="flex items-center justify-center w-8 h-8 bg-white rounded-full shadow-sm">
                  <ArrowRight className="h-3 w-3 text-gray-400" />
                </div>
                <div className="flex-1">
                  <Input
                    type="number"
                    placeholder="最大金额"
                    value={maxAmount}
                    onChange={(e) => setMaxAmount(e.target.value)}
                    className="border-0 bg-white shadow-sm"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">跟进人</label>
              <Select value={followerFilter} onValueChange={setFollowerFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择跟进人" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部跟进人</SelectItem>
                  {followerOptions.map((follower) => (
                    <SelectItem key={follower} value={follower}>
                      {follower}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                <Calendar className="h-4 w-4 text-[#00B4AA]" />
                创建日期范围
              </label>
              <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50/50">
                <div className="flex-1">
                  <Input
                    type="date"
                    value={startCreatedDate}
                    onChange={(e) => setStartCreatedDate(e.target.value)}
                    className="border-0 bg-white shadow-sm"
                    placeholder="开始日期"
                  />
                </div>
                <div className="flex items-center justify-center w-8 h-8 bg-white rounded-full shadow-sm">
                  <ArrowRight className="h-3 w-3 text-gray-400" />
                </div>
                <div className="flex-1">
                  <Input
                    type="date"
                    value={endCreatedDate}
                    onChange={(e) => setEndCreatedDate(e.target.value)}
                    className="border-0 bg-white shadow-sm"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                <CalendarDays className="h-4 w-4 text-[#00B4AA]" />
                状态更新日期范围
              </label>
              <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-gray-50/50">
                <div className="flex-1">
                  <Input
                    type="date"
                    value={startUpdatedDate}
                    onChange={(e) => setStartUpdatedDate(e.target.value)}
                    className="border-0 bg-white shadow-sm"
                    placeholder="开始日期"
                  />
                </div>
                <div className="flex items-center justify-center w-8 h-8 bg-white rounded-full shadow-sm">
                  <ArrowRight className="h-3 w-3 text-gray-400" />
                </div>
                <div className="flex-1">
                  <Input
                    type="date"
                    value={endUpdatedDate}
                    onChange={(e) => setEndUpdatedDate(e.target.value)}
                    className="border-0 bg-white shadow-sm"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 表格 */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-48">项目信息</TableHead>
              <TableHead>客户</TableHead>
              <TableHead>项目状态</TableHead>
              <TableHead>成功几率</TableHead>
              <TableHead>投标金额</TableHead>
              <TableHead>创建日期</TableHead>
              <TableHead>状态更新</TableHead>
              <TableHead>跟进人</TableHead>
              <TableHead className="w-20">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.map((opportunity) => (
              <TableRow 
                key={opportunity.id} 
                className={`hover:bg-gray-50 ${
                  opportunity.projectCode === "ELV-2025-1005" ? "border-l-4 border-l-red-500 bg-red-50/30" : ""
                }`}
              >
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium text-sm">{opportunity.projectName}</div>
                    <div className="text-xs text-gray-500">{opportunity.projectCode}</div>
                    <div className="text-xs text-gray-400">
                      {opportunity.projectTypeName} • {opportunity.elevatorCount}台
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm">{opportunity.customerName}</div>
                    <div className="text-xs text-gray-500">{opportunity.locationName}</div>
                  </div>
                </TableCell>
                
                <TableCell>
                  {projectStatusStyles[opportunity.projectStatus as keyof typeof projectStatusStyles] || 
                   projectStatusStyles["正询价"]}
                </TableCell>
                
                <TableCell>
                  <span className={`text-sm font-medium ${getSuccessProbabilityStyle(opportunity.successProbability || 0)}`}>
                    {opportunity.successProbability || 0}%
                  </span>
                </TableCell>
                
                <TableCell>
                  <span className="text-sm font-medium">{opportunity.estimatedAmount}万元</span>
                </TableCell>
                
                <TableCell>
                  <span className="text-sm text-gray-600">
                    {new Date(opportunity.createdAt).toLocaleDateString('zh-CN')}
                  </span>
                </TableCell>
                
                <TableCell>
                  <span className="text-sm text-gray-600">
                    {opportunity.statusUpdatedAt ? 
                      new Date(opportunity.statusUpdatedAt).toLocaleDateString('zh-CN') :
                      new Date(opportunity.createdAt).toLocaleDateString('zh-CN')
                    }
                  </span>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm font-medium">
                    {opportunity.salesName}
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(opportunity.id)}>
                        <Calculator className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      
                      {opportunity.id === "1003" ? (
                        <>
                          <DropdownMenuItem onClick={() => handleGoToQuotation(opportunity.id)}>
                            <Calculator className="mr-2 h-4 w-4" />
                            查看报价
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={handleViewContract}>
                            <ReceiptText className="mr-2 h-4 w-4" />
                            生成合同
                          </DropdownMenuItem>
                        </>
                      ) : (
                        !opportunity.buttonGroups?.hideElevator && (
                          <DropdownMenuItem onClick={() => handleGoToSelection(opportunity.id)}>
                            <Calculator className="mr-2 h-4 w-4" />
                            {isElevatorSelectionSubmitted(opportunity.id) ? "查看选型" : "电梯选型"}
                          </DropdownMenuItem>
                        )
                      )}
                      
                      {!opportunity.buttonGroups?.hideBlueprint && (
                        <DropdownMenuItem onClick={handleGenerateDrawing}>
                          <Calculator className="mr-2 h-4 w-4" />
                          生成图纸
                        </DropdownMenuItem>
                      )}
                      
                      {opportunity.buttonGroups?.submit && (
                        <DropdownMenuItem onClick={() => handleViewDetails(opportunity.id)}>
                          <Calculator className="mr-2 h-4 w-4" />
                          提交规格
                        </DropdownMenuItem>
                      )}
                      
                      {opportunity.buttonGroups?.contract && (
                        <DropdownMenuItem onClick={handleViewContract}>
                          <Calculator className="mr-2 h-4 w-4" />
                          生成合同
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {filteredData.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            暂无符合条件的商机数据
          </div>
        )}
      </div>
    </div>
  );
} 