"use client";

import { useState } from "react";
import { Dialog, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Check, ChevronRight } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface ProductCategory {
  title: string;
  image: string;
}

interface ProductCategoryModalProps {
  open: boolean;
  onClose: () => void;
  categories: ProductCategory[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

export function ProductCategoryModal({
  open,
  onClose,
  categories,
  activeCategory,
  onCategoryChange,
}: ProductCategoryModalProps) {
  const handleCategorySelect = (category: string) => {
    onCategoryChange(category);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">选择产品类别</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-2 py-4">
          {categories.map((category) => (
            <div
              key={category.title}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200 border hover:border-[#00B4AA]/50 hover:bg-[#E6F7F6]/30",
                activeCategory === category.title
                  ? "border-[#00B4AA] bg-[#E6F7F6]"
                  : "border-gray-200 bg-white hover:shadow-sm"
              )}
              onClick={() => handleCategorySelect(category.title)}
            >
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 relative overflow-hidden rounded-md flex-shrink-0">
                  <Image
                    src={category.image}
                    alt={category.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className={cn(
                    "text-base font-medium",
                    activeCategory === category.title ? "text-[#00B4AA]" : "text-gray-700"
                  )}>
                    {category.title}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {category.title}商机管理
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {activeCategory === category.title && (
                  <div className="w-6 h-6 rounded-full bg-[#00B4AA] flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                )}
                <ChevronRight className={cn(
                  "w-4 h-4 transition-colors",
                  activeCategory === category.title ? "text-[#00B4AA]" : "text-gray-400"
                )} />
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 