"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";
import { CalendarIcon, InfoIcon, PlusCircleIcon, XCircleIcon } from "lucide-react";

interface ContractInfoCollectionProps {
  open: boolean;
  onClose: () => void;
  projectCode: string;
  onSubmit: (data: ContractPaymentTerms) => void;
}

interface Stage {
  name: string;
  percentage: number;
  trigger: string;
}

interface ContractPaymentTerms {
  stages: Stage[];
  latePayment: {
    enabled: boolean;
    rate: number;
    rateUnit: string;
    interestBase: string;
  };
  paymentDocuments: string[];
  retentionMoney: {
    rate: number;
    releaseCondition: string;
  };
  performanceBond: {
    rate: number;
    validUntil: Date | null;
  };
  priceAdjustment: {
    enabled: boolean;
    rules: {
      type: string;
      threshold: number;
    }[];
  };
  insurance: string;
  variationOrder: {
    enabled: boolean;
    description: string;
  };
  disputeResolution: string;
  training: {
    onsite: boolean;
    onsiteDays: number;
    remote: boolean;
    remoteMonths: number;
  };
  spareParts: {
    enabled: boolean;
    years: number;
    extendedWarranty: boolean;
    warrantyMonths: number;
  };
}

export function ContractInfoCollection({
  open,
  onClose,
  projectCode,
  onSubmit,
}: ContractInfoCollectionProps) {
  const [formData, setFormData] = useState<ContractPaymentTerms>({
    stages: [{ name: "签订", percentage: 30, trigger: "合同签订" }],
    latePayment: { enabled: false, rate: 1, rateUnit: "‰/天", interestBase: "同期利率" },
    paymentDocuments: ["请款函", "发票"],
    retentionMoney: { rate: 5, releaseCondition: "验收后12个月" },
    performanceBond: { rate: 10, validUntil: null },
    priceAdjustment: {
      enabled: false,
      rules: [{ type: "钢材", threshold: 5 }],
    },
    insurance: "卖方负责",
    variationOrder: { enabled: false, description: "" },
    disputeResolution: "本地法院",
    training: { onsite: true, onsiteDays: 2, remote: true, remoteMonths: 3 },
    spareParts: { enabled: true, years: 1, extendedWarranty: false, warrantyMonths: 0 },
  });

  const handleSubmit = () => {
    onSubmit(formData);
    onClose();
  };

  const addStage = () => {
    setFormData({
      ...formData,
      stages: [...formData.stages, { name: "", percentage: 0, trigger: "合同签订" }],
    });
  };

  const removeStage = (index: number) => {
    setFormData({
      ...formData,
      stages: formData.stages.filter((_, i) => i !== index),
    });
  };

  const addPriceRule = () => {
    setFormData({
      ...formData,
      priceAdjustment: {
        ...formData.priceAdjustment,
        rules: [...formData.priceAdjustment.rules, { type: "钢材", threshold: 0 }],
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        // className="w-[95vw] max-h-[90vh] overflow-y-auto p-6"
        className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto mx-auto w-[95%] md:w-full"
        style={{ maxWidth: "1400px !important" }}
      >
        <DialogHeader className="mb-6">
          <DialogTitle className="text-2xl font-bold text-[#00B4AA]">
            {projectCode} 合同付款条件定义
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 1. 多阶段付款结构 */}
          <Accordion type="single" collapsible defaultValue="item-1" className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger>多阶段付款结构</AccordionTrigger>
              <AccordionContent>
                {formData.stages.map((stage, index) => (
                  <div key={index} className="flex items-center gap-4 mb-4">
                    <div className="w-6">{index + 1}.</div>
                    <div className="flex-1 grid grid-cols-3 gap-5">
                      <div className="space-y-3">
                        <Label className="text-sm font-medium">阶段名称</Label>
                        <Input
                          value={stage.name}
                          onChange={e => {
                            const newStages = [...formData.stages];
                            newStages[index].name = e.target.value;
                            setFormData({ ...formData, stages: newStages });
                          }}
                          placeholder="如：签订、发货前"
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-medium">
                          支付比例 ({stage.percentage}%)
                        </Label>
                        <Slider
                          value={[stage.percentage]}
                          min={20}
                          max={100}
                          step={5}
                          className="mt-6"
                          onValueChange={value => {
                            const newStages = [...formData.stages];
                            newStages[index].percentage = value[0];
                            setFormData({ ...formData, stages: newStages });
                          }}
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-medium">触发条件</Label>
                        <Select
                          value={stage.trigger}
                          onValueChange={value => {
                            const newStages = [...formData.stages];
                            newStages[index].trigger = value;
                            setFormData({ ...formData, stages: newStages });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="合同签订">合同签订</SelectItem>
                            <SelectItem value="发货通知">发货通知</SelectItem>
                            <SelectItem value="验收确认">验收确认</SelectItem>
                            <SelectItem value="质保期满">质保期满</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => removeStage(index)}>
                      <XCircleIcon className="h-5 w-5" />
                    </Button>
                  </div>
                ))}

                <Button variant="outline" onClick={addStage} className="mt-2">
                  <PlusCircleIcon className="h-4 w-4 mr-2" />
                  添加阶段
                </Button>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* 2. 逾期付款违约金及利息 */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-md font-medium">逾期付款违约金及利息</h3>
              <div className="flex items-center gap-2">
                <Switch
                  checked={formData.latePayment.enabled}
                  onCheckedChange={checked => {
                    setFormData({
                      ...formData,
                      latePayment: {
                        ...formData.latePayment,
                        enabled: checked,
                      },
                    });
                  }}
                />
                <Label>启用逾期罚息</Label>
              </div>
            </div>

            {formData.latePayment.enabled && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>违约金率</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      value={formData.latePayment.rate}
                      onChange={e => {
                        setFormData({
                          ...formData,
                          latePayment: {
                            ...formData.latePayment,
                            rate: parseFloat(e.target.value),
                          },
                        });
                      }}
                    />
                    <Select
                      value={formData.latePayment.rateUnit}
                      onValueChange={value => {
                        setFormData({
                          ...formData,
                          latePayment: {
                            ...formData.latePayment,
                            rateUnit: value,
                          },
                        });
                      }}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="‰/天">‰/天</SelectItem>
                        <SelectItem value="%/天">%/天</SelectItem>
                        <SelectItem value="%/月">%/月</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label>利息基准</Label>
                  <Select
                    value={formData.latePayment.interestBase}
                    onValueChange={value => {
                      setFormData({
                        ...formData,
                        latePayment: {
                          ...formData.latePayment,
                          interestBase: value,
                        },
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="同期利率">银行同期贷款利率</SelectItem>
                      <SelectItem value="LPR">LPR+100基点</SelectItem>
                      <SelectItem value="自定义">自定义年利率</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>

          {/* 3. 付款单据与资质文件 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-md font-medium mb-4">付款单据与资质文件</h3>
            <div className="grid grid-cols-2 gap-y-2">
              {["请款函", "原产地证", "装箱单", "合格证", "质保证书", "发票"].map(doc => (
                <div key={doc} className="flex items-center gap-2">
                  <Checkbox
                    id={doc}
                    checked={formData.paymentDocuments.includes(doc)}
                    onCheckedChange={checked => {
                      if (checked) {
                        setFormData({
                          ...formData,
                          paymentDocuments: [...formData.paymentDocuments, doc],
                        });
                      } else {
                        setFormData({
                          ...formData,
                          paymentDocuments: formData.paymentDocuments.filter(d => d !== doc),
                        });
                      }
                    }}
                  />
                  <Label htmlFor={doc}>{doc}</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-gray-400 cursor-help" />
                    </PopoverTrigger>
                    <PopoverContent className="w-80">
                      <div className="space-y-2">
                        <h4 className="font-medium">{doc}要求</h4>
                        <p className="text-sm text-gray-500">
                          {doc === "请款函"
                            ? "需盖章原件，提供电子版和纸质版"
                            : doc === "发票"
                            ? "增值税专用发票，税率13%"
                            : "需按标准格式提供盖章文件"}
                        </p>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              ))}
            </div>
          </div>

          {/* 4. 保留金 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-md font-medium mb-4">保留金（Retention Money）</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>保留比例</Label>
                <RadioGroup
                  value={formData.retentionMoney.rate.toString()}
                  onValueChange={value => {
                    setFormData({
                      ...formData,
                      retentionMoney: {
                        ...formData.retentionMoney,
                        rate: parseInt(value),
                      },
                    });
                  }}
                  className="flex gap-4 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="5" id="r5" />
                    <Label htmlFor="r5">5%</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="10" id="r10" />
                    <Label htmlFor="r10">10%</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="custom" id="rCustom" />
                    <Input
                      type="number"
                      className="w-20"
                      placeholder="自定义"
                      value={
                        formData.retentionMoney.rate !== 5 && formData.retentionMoney.rate !== 10
                          ? formData.retentionMoney.rate
                          : ""
                      }
                      onChange={e => {
                        setFormData({
                          ...formData,
                          retentionMoney: {
                            ...formData.retentionMoney,
                            rate: parseInt(e.target.value),
                          },
                        });
                      }}
                    />
                    <span>%</span>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>释放条件</Label>
                <Select
                  value={formData.retentionMoney.releaseCondition}
                  onValueChange={value => {
                    setFormData({
                      ...formData,
                      retentionMoney: {
                        ...formData.retentionMoney,
                        releaseCondition: value,
                      },
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="验收后12个月">验收后12个月</SelectItem>
                    <SelectItem value="分两次释放">分两次释放</SelectItem>
                    <SelectItem value="质保期满">质保期满</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 5. 履约保证金/银行保函 */}
          <div className="border rounded-lg p-4">
            <h3 className="text-md font-medium mb-4">履约保证金／银行保函</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>保函比例</Label>
                <RadioGroup
                  value={formData.performanceBond.rate.toString()}
                  onValueChange={value => {
                    setFormData({
                      ...formData,
                      performanceBond: {
                        ...formData.performanceBond,
                        rate: parseInt(value),
                      },
                    });
                  }}
                  className="flex gap-4 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="5" id="p5" />
                    <Label htmlFor="p5">5%</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="10" id="p10" />
                    <Label htmlFor="p10">10%</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="custom" id="pCustom" />
                    <Input
                      type="number"
                      className="w-20"
                      placeholder="自定义"
                      value={
                        formData.performanceBond.rate !== 5 && formData.performanceBond.rate !== 10
                          ? formData.performanceBond.rate
                          : ""
                      }
                      onChange={e => {
                        setFormData({
                          ...formData,
                          performanceBond: {
                            ...formData.performanceBond,
                            rate: parseInt(e.target.value),
                          },
                        });
                      }}
                    />
                    <span>%</span>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>保函有效期至</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal mt-2"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.performanceBond.validUntil ? (
                        format(formData.performanceBond.validUntil, "yyyy-MM-dd")
                      ) : (
                        <span>选择日期</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.performanceBond.validUntil || undefined}
                      onSelect={date => {
                        setFormData({
                          ...formData,
                          performanceBond: {
                            ...formData.performanceBond,
                            validUntil: date,
                          },
                        });
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* 6-11. 其他条款简化为AccordionItems */}
          <Accordion type="multiple" className="w-full">
            {/* 6. 价格调整条款 */}
            <AccordionItem value="item-6">
              <AccordionTrigger>价格调整条款</AccordionTrigger>
              <AccordionContent>
                <div className="flex items-center gap-2 mb-4">
                  <Switch
                    checked={formData.priceAdjustment.enabled}
                    onCheckedChange={checked => {
                      setFormData({
                        ...formData,
                        priceAdjustment: {
                          ...formData.priceAdjustment,
                          enabled: checked,
                        },
                      });
                    }}
                  />
                  <Label>启用价格调整机制</Label>
                </div>

                {formData.priceAdjustment.enabled && (
                  <>
                    {formData.priceAdjustment.rules.map((rule, index) => (
                      <div key={index} className="flex items-center gap-4 mb-4">
                        <div className="flex-1 grid grid-cols-2 gap-4">
                          <div>
                            <Label>调整类型</Label>
                            <Select
                              value={rule.type}
                              onValueChange={value => {
                                const newRules = [...formData.priceAdjustment.rules];
                                newRules[index].type = value;
                                setFormData({
                                  ...formData,
                                  priceAdjustment: {
                                    ...formData.priceAdjustment,
                                    rules: newRules,
                                  },
                                });
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="钢材">钢材指数</SelectItem>
                                <SelectItem value="铜">铜价指数</SelectItem>
                                <SelectItem value="汇率">汇率变动</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>浮动阈值</Label>
                            <div className="flex items-center gap-2">
                              <Input
                                type="number"
                                value={rule.threshold}
                                onChange={e => {
                                  const newRules = [...formData.priceAdjustment.rules];
                                  newRules[index].threshold = parseFloat(e.target.value);
                                  setFormData({
                                    ...formData,
                                    priceAdjustment: {
                                      ...formData.priceAdjustment,
                                      rules: newRules,
                                    },
                                  });
                                }}
                              />
                              <span>%</span>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setFormData({
                              ...formData,
                              priceAdjustment: {
                                ...formData.priceAdjustment,
                                rules: formData.priceAdjustment.rules.filter((_, i) => i !== index),
                              },
                            });
                          }}
                        >
                          <XCircleIcon className="h-5 w-5" />
                        </Button>
                      </div>
                    ))}

                    <Button variant="outline" onClick={addPriceRule}>
                      <PlusCircleIcon className="h-4 w-4 mr-2" />
                      添加规则
                    </Button>
                  </>
                )}
              </AccordionContent>
            </AccordionItem>

            {/* 7-11. 其他条款（简化版本） */}
            <AccordionItem value="item-7">
              <AccordionTrigger>保险与风险转移</AccordionTrigger>
              <AccordionContent>
                <RadioGroup
                  value={formData.insurance}
                  onValueChange={value => {
                    setFormData({
                      ...formData,
                      insurance: value,
                    });
                  }}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="卖方负责" id="ins1" />
                    <Label htmlFor="ins1">卖方负责运输险</Label>
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <RadioGroupItem value="买方自提" id="ins2" />
                    <Label htmlFor="ins2">买方自提</Label>
                  </div>
                </RadioGroup>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-8">
              <AccordionTrigger>变更／追加工程</AccordionTrigger>
              <AccordionContent>
                <div className="flex items-center gap-2 mb-4">
                  <Switch
                    checked={formData.variationOrder.enabled}
                    onCheckedChange={checked => {
                      setFormData({
                        ...formData,
                        variationOrder: {
                          ...formData.variationOrder,
                          enabled: checked,
                        },
                      });
                    }}
                  />
                  <Label>启用变更流程</Label>
                </div>

                {formData.variationOrder.enabled && (
                  <div>
                    <Label>变更费用标准及流程说明</Label>
                    <Textarea
                      value={formData.variationOrder.description}
                      onChange={e => {
                        setFormData({
                          ...formData,
                          variationOrder: {
                            ...formData.variationOrder,
                            description: e.target.value,
                          },
                        });
                      }}
                      placeholder="请输入变更费用标准、签署流程等条款说明"
                      className="min-h-[100px] mt-2"
                    />
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-9">
              <AccordionTrigger>争议解决方式</AccordionTrigger>
              <AccordionContent>
                <div className="flex items-center gap-4">
                  <Select
                    value={formData.disputeResolution}
                    onValueChange={value => {
                      setFormData({
                        ...formData,
                        disputeResolution: value,
                      });
                    }}
                  >
                    <SelectTrigger className="w-56">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="本地法院">本地法院</SelectItem>
                      <SelectItem value="ICC仲裁">ICC仲裁</SelectItem>
                      <SelectItem value="VIAC仲裁">VIAC仲裁</SelectItem>
                      <SelectItem value="调解优先">调解优先</SelectItem>
                    </SelectContent>
                  </Select>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="icon">
                        <InfoIcon className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80">
                      <div className="space-y-2">
                        <h4 className="font-medium">争议解决方式对比</h4>
                        <p className="text-sm">
                          <strong>本地法院：</strong> 成本低但程序时间长
                          <br />
                          <strong>ICC仲裁：</strong> 国际认可度高但费用高
                          <br />
                          <strong>调解优先：</strong> 先协商调解，无果再诉诸法律
                        </p>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-10">
              <AccordionTrigger>培训及技术支持</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={formData.training.onsite}
                      onCheckedChange={checked => {
                        setFormData({
                          ...formData,
                          training: {
                            ...formData.training,
                            onsite: checked as boolean,
                          },
                        });
                      }}
                      id="onsite"
                    />
                    <Label htmlFor="onsite">现场培训</Label>

                    {formData.training.onsite && (
                      <div className="flex items-center ml-4">
                        <Select
                          value={formData.training.onsiteDays.toString()}
                          onValueChange={value => {
                            setFormData({
                              ...formData,
                              training: {
                                ...formData.training,
                                onsiteDays: parseInt(value),
                              },
                            });
                          }}
                        >
                          <SelectTrigger className="w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {[1, 2, 3, 4, 5].map(day => (
                              <SelectItem key={day} value={day.toString()}>
                                {day}天
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={formData.training.remote}
                      onCheckedChange={checked => {
                        setFormData({
                          ...formData,
                          training: {
                            ...formData.training,
                            remote: checked as boolean,
                          },
                        });
                      }}
                      id="remote"
                    />
                    <Label htmlFor="remote">远程支持</Label>

                    {formData.training.remote && (
                      <div className="flex items-center ml-4">
                        <Select
                          value={formData.training.remoteMonths.toString()}
                          onValueChange={value => {
                            setFormData({
                              ...formData,
                              training: {
                                ...formData.training,
                                remoteMonths: parseInt(value),
                              },
                            });
                          }}
                        >
                          <SelectTrigger className="w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {[1, 3, 6, 12].map(month => (
                              <SelectItem key={month} value={month.toString()}>
                                {month}个月
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-11">
              <AccordionTrigger>备件供应及质保延长</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={formData.spareParts.enabled}
                      onCheckedChange={checked => {
                        setFormData({
                          ...formData,
                          spareParts: {
                            ...formData.spareParts,
                            enabled: checked,
                          },
                        });
                      }}
                    />
                    <Label>备件保障</Label>

                    {formData.spareParts.enabled && (
                      <div className="flex items-center ml-4">
                        <Label className="mr-2">年限</Label>
                        <Select
                          value={formData.spareParts.years.toString()}
                          onValueChange={value => {
                            setFormData({
                              ...formData,
                              spareParts: {
                                ...formData.spareParts,
                                years: parseInt(value),
                              },
                            });
                          }}
                        >
                          <SelectTrigger className="w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {[1, 2, 3, 4, 5].map(year => (
                              <SelectItem key={year} value={year.toString()}>
                                {year}年
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Switch
                      checked={formData.spareParts.extendedWarranty}
                      onCheckedChange={checked => {
                        setFormData({
                          ...formData,
                          spareParts: {
                            ...formData.spareParts,
                            extendedWarranty: checked,
                          },
                        });
                      }}
                    />
                    <Label>延长质保</Label>

                    {formData.spareParts.extendedWarranty && (
                      <div className="flex items-center ml-4">
                        <Label className="mr-2">延长</Label>
                        <Input
                          type="number"
                          className="w-20"
                          value={formData.spareParts.warrantyMonths}
                          onChange={e => {
                            setFormData({
                              ...formData,
                              spareParts: {
                                ...formData.spareParts,
                                warrantyMonths: parseInt(e.target.value),
                              },
                            });
                          }}
                        />
                        <span className="ml-2">个月</span>
                      </div>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handleSubmit}>开始生成</Button>
        </DialogFooter>

        {/* 调整多列表单项的响应式布局 */}
        <style jsx global>{`
          @media (max-width: 768px) {
            .grid-cols-2,
            .grid-cols-3 {
              grid-template-columns: 1fr !important;
            }
          }
        `}</style>
      </DialogContent>
    </Dialog>
  );
}
