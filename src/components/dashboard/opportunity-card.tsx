"use client";

import { Badge } from "../ui/badge";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from "../ui/card";
import {
  CheckCircle2,
  Clock3,
  Loader2,
  Calculator,
  Edit,
  ArrowRight,
  Building,
  Phone,
  Mail,
  MapPin,
  Captions,
  ChevronDown,
  Pencil,
  MoreHorizontal,
  CircleDashed,
  ReceiptText,
  Download,
} from "lucide-react";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { hasElevatorSelection, isElevatorSelectionSubmitted } from "@/lib/elevator-selection";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import { ContractInfoCollection } from "./contract-info-collection";

// 项目状态样式 - 根据业务流程更新
const projectStatusStyles = {
  正询价: (
    <Badge variant="outline" className="bg-[#E3F2FD]/50 text-[#1976D2] border-[#E3F2FD] text-xs">
      正询价 (Inquiry)
    </Badge>
  ),
  已报价: (
    <Badge variant="outline" className="bg-[#F3E5F5]/50 text-[#7B1FA2] border-[#F3E5F5] text-xs">
      已报价 (Quotation)
    </Badge>
  ),
  已出图: (
    <Badge variant="outline" className="bg-[#E8F5E8]/50 text-[#388E3C] border-[#E8F5E8] text-xs">
      已出图 (Designed)
    </Badge>
  ),
  需求变更: (
    <Badge variant="outline" className="bg-[#FFF3E0]/50 text-[#F57C00] border-[#FFF3E0] text-xs">
      需求变更 (Change requirement)
    </Badge>
  ),
  已签约: (
    <Badge variant="outline" className="bg-[#E0F2F1]/50 text-[#00695C] border-[#E0F2F1] text-xs">
      已签约 (Contract signed)
    </Badge>
  ),
  已首付: (
    <Badge variant="outline" className="bg-[#E1F5FE]/50 text-[#0277BD] border-[#E1F5FE] text-xs">
      已首付 (Down payment)
    </Badge>
  ),
  图纸确认: (
    <Badge variant="outline" className="bg-[#F8BBD0]/50 text-[#E91E63] border-[#F8BBD0] text-xs">
      图纸确认 (GAD confirmed)
    </Badge>
  ),
  已排产: (
    <Badge variant="outline" className="bg-[#FFECB3]/50 text-[#FF9800] border-[#FFECB3] text-xs">
      已排产 (Production)
    </Badge>
  ),
  已发货: (
    <Badge variant="outline" className="bg-[#B2DFDB]/50 text-[#00B4AA] border-[#B2DFDB] text-xs">
      已发货 (Delivery)
    </Badge>
  ),
  已失失: (
    <Badge variant="outline" className="bg-[#FFCDD2]/50 text-[#D32F2F] border-[#FFCDD2] text-xs">
      已失失 (Lost)
    </Badge>
  ),
};

// 成功几率颜色样式
const getSuccessProbabilityStyle = (probability: number) => {
  if (probability >= 80) {
    return "text-[#2E7D32] font-semibold"; // 深绿色 - 优秀
  } else if (probability >= 60) {
    return "text-[#388E3C] font-semibold"; // 中绿色 - 良好
  } else if (probability >= 40) {
    return "text-[#F57C00] font-semibold"; // 橙色 - 一般
  } else {
    return "text-[#D32F2F] font-semibold"; // 红色 - 较低
  }
};

// 商机数据类型
export interface OpportunityData {
  id: string;
  projectCode: string;
  projectName: string;
  customerName: string;
  projectManagerName: string;
  salesName: string;
  salesAssistantName?: string;
  progress: number;
  status: string;
  createdAt: string;
  expectedSignDate: string;
  estimatedAmount: string;
  elevatorCount: string;
  projectTypeName: string;
  customerTypeName?: string;
  locationName?: string;
  projectAddress?: string;
  engineeringTypeName?: string;
  industryTypeName?: string;
  isLargeProjectName?: string;
  contractCode?: string;
  contactPhone?: string;
  contactEmail?: string;
  buttonGroups?: ButtonGroups;
  successProbability?: number; // 成功几率
  projectStatus?: string; // 项目状态
  statusUpdatedAt?: string; // 状态更新日期
  // 其他字段...
}

export interface ButtonGroups {
  detail: boolean;
  submit: boolean;
  contract: boolean;
  hideBlueprint: boolean;
  hideElevator: boolean;
}

interface OpportunityCardProps {
  data: OpportunityData;
  onUpdate: () => void;
  categoryName?: string;
  buttonGroups?: ButtonGroups;
}

// 编辑商机的schema
const editOpportunitySchema = z.object({
  projectName: z.string().min(2, { message: "项目名称至少需要2个字符" }),
  customerName: z.string().min(2, { message: "客户名称至少需要2个字符" }),
  projectTypeName: z.string().min(1, { message: "请选择项目类型" }),
  elevatorCount: z.string().min(1, { message: "请输入电梯数量" }),
  estimatedAmount: z.string().min(1, { message: "请输入投标额" }),
  projectAddress: z.string().optional(),
  contactPhone: z.string().optional(),
  contactEmail: z.string().email({ message: "请输入有效的邮箱地址" }).optional().or(z.literal("")),
  successProbability: z.number().min(0).max(100).optional(),
  projectStatus: z.string().optional(),
});

export function OpportunityCard({
  data,
  onUpdate,
  categoryName = "电梯",
  buttonGroups,
}: OpportunityCardProps) {
  const router = useRouter();
  const [opportunityData, setOpportunityData] = useState<OpportunityData>(data);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openContractDialog, setOpenContractDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSelectionSubmitted, setIsSelectionSubmitted] = useState(false);
  const [openDrawingDialog, setOpenDrawingDialog] = useState(false);
  const [showContractSettingsDialog, setShowContractSettingsDialog] = useState(false);
  const [contractPaymentTerms, setContractPaymentTerms] = useState<any>(null);

  // 当外部data变化时，同步更新本地状态
  useEffect(() => {
    setOpportunityData(data);
  }, [data]);

  // 检查电梯选型状态
  useEffect(() => {
    // 检查是否已提交选型
    setIsSelectionSubmitted(isElevatorSelectionSubmitted(opportunityData.id));

    // 为第一个商机（万科城市之光，ID: 1001）设置一个默认的已提交电梯选型
    if (opportunityData.id === "1001" && !isElevatorSelectionSubmitted("1001")) {
      // 创建默认的电梯选型数据
      const defaultElevatorSelection = {
        liftModel: "MK8000",
        capacity: 1000,
        speed: 1.75,
        travelHeight: 60,
        carWidth: 1600,
        carDepth: 1500,
        carHeight: 2300,
        cwtPosition: "SIDE",
        cwtSafetyGear: false,
        doorOpening: "CO",
        doorWidth: 900,
        doorHeight: 2100,
        throughDoor: false,
        glassDoor: false,
        standard: "EN81",
        doorCenterPosition: "CENTERED",
        floorExceedCode: false,
        shaftTolerance: "NORMAL",
        marbleFloorThickness: 40,
        isSubmitted: true,
        submittedAt: new Date().toISOString(),
      };

      // 保存到localStorage
      localStorage.setItem(`elevatorSelectionData-1001`, JSON.stringify(defaultElevatorSelection));

      // 更新提交状态映射表
      let submittedOpportunities: { [key: string]: boolean } = {};
      try {
        const saved = localStorage.getItem("submittedElevatorSelections");
        if (saved) {
          submittedOpportunities = JSON.parse(saved);
        }
      } catch (e) {
        console.error("读取提交状态失败", e);
      }

      // 添加1001商机的提交状态
      submittedOpportunities["1001"] = true;
      localStorage.setItem("submittedElevatorSelections", JSON.stringify(submittedOpportunities));

      // 重新检查状态
      setIsSelectionSubmitted(true);
    }
  }, [opportunityData.id]);

  // 编辑表单
  const editForm = useForm<z.infer<typeof editOpportunitySchema>>({
    resolver: zodResolver(editOpportunitySchema),
    defaultValues: {
      projectName: opportunityData.projectName,
      customerName: opportunityData.customerName,
      projectTypeName: opportunityData.projectTypeName,
      elevatorCount: opportunityData.elevatorCount,
      estimatedAmount: opportunityData.estimatedAmount,
      projectAddress: opportunityData.projectAddress || "",
      contactPhone: opportunityData.contactPhone || "",
      contactEmail: opportunityData.contactEmail || "",
      successProbability: opportunityData.successProbability || 0,
      projectStatus: opportunityData.projectStatus || "正询价",
    },
  });

  // 编辑商机信息
  const handleEditOpportunity = (values: z.infer<typeof editOpportunitySchema>) => {
    setIsLoading(true);
    try {
      // 获取现有商机
      const existingOpportunities = JSON.parse(localStorage.getItem("opportunities") || "[]");

      // 找到并更新当前商机
      const updatedOpportunities = existingOpportunities.map((opp: OpportunityData) =>
        opp.id === opportunityData.id
          ? {
              ...opp,
              projectName: values.projectName,
              customerName: values.customerName,
              projectTypeName: values.projectTypeName,
              elevatorCount: values.elevatorCount,
              estimatedAmount: values.estimatedAmount,
              projectAddress: values.projectAddress,
              contactPhone: values.contactPhone,
              contactEmail: values.contactEmail,
              successProbability: values.successProbability,
              projectStatus: values.projectStatus,
            }
          : opp
      );

      // 保存回localStorage
      localStorage.setItem("opportunities", JSON.stringify(updatedOpportunities));

      // 更新本地状态
      setOpportunityData({
        ...opportunityData,
        projectName: values.projectName,
        customerName: values.customerName,
        projectTypeName: values.projectTypeName,
        elevatorCount: values.elevatorCount,
        estimatedAmount: values.estimatedAmount,
        projectAddress: values.projectAddress,
        contactPhone: values.contactPhone,
        contactEmail: values.contactEmail,
        successProbability: values.successProbability,
        projectStatus: values.projectStatus,
      });

      toast.success("商机信息更新成功！");
      onUpdate(); // 通知父组件刷新数据
      setOpenEditDialog(false);
    } catch (error) {
      console.error("更新商机信息失败:", error);
      toast.error("更新商机信息失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  // 前往电梯选型页面
  const handleGoToSelection = () => {
    router.push(`/dashboard/opportunity/${opportunityData.id}/elevator-selection`);
  };

  // 前往报价页面
  const handleGoToQuotation = () => {
    router.push(`/dashboard/opportunity/${opportunityData.id}/quotation`);
  };

  // 处理查看合同
  const handleViewContract = () => {
    setShowContractSettingsDialog(true);
  };

  // 合同付款条件提交后的处理函数
  const handleContractInfoSubmit = (paymentTerms: any) => {
    setContractPaymentTerms(paymentTerms);
    setShowContractSettingsDialog(false);
    setOpenContractDialog(true);
  };

  // 处理下载合同
  const handleDownloadContract = () => {
    // 创建一个链接指向PDF文件
    const link = document.createElement("a");
    link.href =
      "/data/20250430 - MINH - HĐ lắp đặt-Huayang客户-ocean park 项目电梯采购与安装合同.LS";
    link.download = "20250430 - MINH - HĐ lắp đặt-Huayang客户-ocean park 项目电梯采购与安装合同.LS";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 修改处理生成图纸的函数
  const handleGenerateDrawing = () => {
    setOpenDrawingDialog(true);
  };

  // 添加下载图纸的处理函数
  const handleDownloadDrawing = () => {
    // 创建一个链接指向图片文件
    const link = document.createElement("a");
    link.href = "/data/cad.jpg"; // 替换为实际的图片路径
    link.download = "电梯平面图.jpg"; // 下载时的文件名
    document.body.appendChild(link);
    link.click();
  };

  return (
    <>
      <Card
        className={`shadow-md w-full gap-2 h-full flex flex-col justify-between transition-transform hover:scale-[0.99] @container/card ${
          opportunityData.projectCode === "ELV-2025-1005" ? "border-red-500 border-2" : ""
        }`}
      >
        <CardHeader className="pb-2 @3xl:p-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center @3xl:text-base">
              {opportunityData.projectName || "未命名项目"}
            </CardTitle>
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => router.push(`/dashboard/opportunity/${opportunityData.id}`)}>
                    <Calculator className="mr-2 h-4 w-4" />
                    查看详情
                  </DropdownMenuItem>
                  
                  {data.id === "1003" ? (
                    <>
                      <DropdownMenuItem onClick={handleGoToQuotation}>
                        <Calculator className="mr-2 h-4 w-4" />
                        查看报价
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleViewContract}>
                        <ReceiptText className="mr-2 h-4 w-4" />
                        生成合同
                      </DropdownMenuItem>
                    </>
                  ) : (
                    !buttonGroups?.hideElevator && (
                      <DropdownMenuItem onClick={handleGoToSelection}>
                        <Calculator className="mr-2 h-4 w-4" />
                        {isSelectionSubmitted ? "查看选型" : "电梯选型"}
                      </DropdownMenuItem>
                    )
                  )}
                  
                  {!buttonGroups?.hideBlueprint && (
                    <DropdownMenuItem onClick={handleGenerateDrawing}>
                      <Calculator className="mr-2 h-4 w-4" />
                      生成图纸
                    </DropdownMenuItem>
                  )}
                  
                  {buttonGroups?.submit && (
                    <DropdownMenuItem onClick={() => router.push(`/dashboard/opportunity/${opportunityData.id}`)}>
                      <Calculator className="mr-2 h-4 w-4" />
                      提交规格
                    </DropdownMenuItem>
                  )}
                  
                  {buttonGroups?.contract && (
                    <DropdownMenuItem onClick={handleViewContract}>
                      <Calculator className="mr-2 h-4 w-4" />
                      生成合同
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuItem onClick={() => setOpenEditDialog(true)}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑信息
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <CardDescription>
            <div className="flex flex-row items-center text-xs">
              <span className="text-sm @3xl:text-xs font-medium mr-1">项目号:</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-sm @3xl:text-xs font-bold">
                      {opportunityData.projectCode.slice(0, 10)}...
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <span className="text-sm @3xl:text-xs font-bold">
                      {opportunityData.projectCode}
                    </span>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <span className="mx-2 text-[#E8E8E8]">|</span>
              <span className="text-sm @3xl:text-xs font-medium mr-1">客户:</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-sm @3xl:text-xs">
                      {opportunityData.customerName.slice(0, 10)}...
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <span className="text-sm @3xl:text-xs">{opportunityData.customerName}</span>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            {/* Add error message for the specific project */}
            {opportunityData.projectCode === "ELV-2025-1005" && (
              <div className="text-red-500 font-medium mt-1">
                潛在衝突商機：請參考項目號ELV-2025-77777
              </div>
            )}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="@3xl:p-3 mt-2 flex-1">
          {/* 项目状态和成功几率 */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <span className="text-xs text-[#505050]">项目状态</span>
              {projectStatusStyles[opportunityData.projectStatus as keyof typeof projectStatusStyles] || 
               projectStatusStyles["正询价"]}
            </div>
            <div className="flex flex-col gap-1 text-right">
              <span className="text-xs text-[#505050]">成功几率</span>
              <span className={`text-sm font-medium ${getSuccessProbabilityStyle(opportunityData.successProbability || 0)}`}>
                {opportunityData.successProbability || 0}%
              </span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-x-2 gap-y-3 mt-2">
            <div className="flex items-center gap-1 text-[#505050]">
              <Building className="h-3.5 w-3.5 flex-shrink-0" />
              <span className="text-xs overflow-hidden text-ellipsis whitespace-nowrap">
                {opportunityData.engineeringTypeName || "未指定"}
              </span>
            </div>
            <div className="flex items-center gap-1 text-[#505050] justify-end">
              <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
              <span className="text-xs overflow-hidden text-ellipsis whitespace-nowrap">
                {opportunityData.locationName || "未指定"}
              </span>
            </div>
            
            <div className="flex flex-col">
              <span className="text-xs text-[#505050]">项目类型</span>
              <span className="text-sm font-medium">{opportunityData.projectTypeName}</span>
            </div>
            <div className="flex flex-col text-right">
              <span className="text-xs text-[#505050]">{categoryName}数量</span>
              <span className="text-sm font-medium">{opportunityData.elevatorCount}台</span>
            </div>

            <div className="flex flex-col">
              <span className="text-xs text-[#505050]">总投标额</span>
              <span className="text-sm font-medium">{opportunityData.estimatedAmount}万元</span>
            </div>
            <div className="flex flex-col text-right">
              <span className="text-xs text-[#505050]">预计签约</span>
              <span className="text-sm font-medium">
                {opportunityData.expectedSignDate.split("T")[0]}
              </span>
            </div>
          </div>

          <Separator className="my-3 bg-[#E8E8E8]" />

          <div className="flex flex-col gap-1.5">
            <div className="flex items-center gap-1">
              <span className="text-xs font-medium">跟进人:</span>
              <span className="text-xs">
                {opportunityData.salesName}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-xs font-medium">创建日期:</span>
              <span className="text-xs">
                {new Date(opportunityData.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 编辑商机信息对话框 */}
      <Dialog open={openEditDialog} onOpenChange={setOpenEditDialog}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>编辑商机信息</DialogTitle>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditOpportunity)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="projectName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>项目名称</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="customerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>客户名称</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="projectTypeName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>项目类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择项目类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="住宅">住宅</SelectItem>
                          <SelectItem value="商业">商业</SelectItem>
                          <SelectItem value="工业">工业</SelectItem>
                          <SelectItem value="公共建筑">公共建筑</SelectItem>
                          <SelectItem value="改造项目">改造项目</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="elevatorCount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{categoryName}数量</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="estimatedAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>总投标额(万元)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="successProbability"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>成功几率(%)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="0" 
                          max="100" 
                          {...field} 
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="projectStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>项目状态</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择项目状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="正询价">正询价 (Inquiry)</SelectItem>
                          <SelectItem value="已报价">已报价 (Quotation)</SelectItem>
                          <SelectItem value="已出图">已出图 (Designed)</SelectItem>
                          <SelectItem value="需求变更">需求变更 (Change requirement)</SelectItem>
                          <SelectItem value="已签约">已签约 (Contract signed)</SelectItem>
                          <SelectItem value="已首付">已首付 (Down payment)</SelectItem>
                          <SelectItem value="图纸确认">图纸确认 (GAD confirmed)</SelectItem>
                          <SelectItem value="已排产">已排产 (Production)</SelectItem>
                          <SelectItem value="已发货">已发货 (Delivery)</SelectItem>
                          <SelectItem value="已失失">已失失 (Lost)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="projectAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>项目地址</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系邮箱</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setOpenEditDialog(false)} type="button">
                  取消
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "更新中..." : "保存修改"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 付款条件定义弹窗 */}
      <ContractInfoCollection
        open={showContractSettingsDialog}
        onClose={() => setShowContractSettingsDialog(false)}
        projectCode={data.projectCode}
        onSubmit={handleContractInfoSubmit}
      />

      {/* 原有的合同查看弹窗 */}
      <Dialog open={openContractDialog} onOpenChange={setOpenContractDialog}>
        <DialogContent className="max-w-[95vw] max-h-[90vh] w-[1400px] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>合同文件</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-auto p-1 min-h-[700px]">
            <iframe
              src="/data/20250430 - MINH - HĐ lắp đặt-Huayang客户-ocean park 项目电梯采购与安装合同.LS.pdf"
              width="100%"
              height="100%"
              style={{ minHeight: "700px", border: "none" }}
            />
          </div>
          <DialogFooter className="mt-4">
            <Button onClick={handleDownloadContract} variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              下载合同
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 修改图纸查看对话框 */}
      <Dialog open={openDrawingDialog} onOpenChange={setOpenDrawingDialog}>
        <DialogContent className="!max-w-[90vw] max-h-[85vh] w-[1600px] overflow-hidden flex flex-col">
          <DialogHeader className="pb-2">
            <DialogTitle>电梯平面图</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-auto p-4 min-h-[500px] bg-gray-50 rounded-md">
            <div className="w-full h-full flex items-center justify-center">
              <img
                src="/data/cad.jpg" // 替换为实际的图片路径
                alt="电梯平面图"
                className="w-full h-auto object-contain"
                style={{
                  maxHeight: "calc(85vh - 140px)", // 减去header和footer的高度
                }}
              />
            </div>
          </div>
          <DialogFooter className="mt-2">
            <Button
              onClick={handleDownloadDrawing}
              variant="outline"
              className="gap-2 bg-white hover:bg-gray-50"
            >
              <Download className="h-4 w-4" />
              下载图纸
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
