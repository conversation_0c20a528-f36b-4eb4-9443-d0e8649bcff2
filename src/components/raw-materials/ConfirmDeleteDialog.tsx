"use client"

import {ExtendedRawMaterialInfo} from "@/api/suppliers/types"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {Loader2} from "lucide-react"


interface ConfirmDeleteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  raw_material: ExtendedRawMaterialInfo | null
  onConfirm: () => void
  isDeleting: boolean
}

export function ConfirmDeleteDialog({
  open,
  onOpenChange,
  raw_material,
  onConfirm,
  isDeleting,
}: ConfirmDeleteDialogProps) {
  if (!raw_material) return null; // 如果没有原材料信息，不渲染对话框

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除原材料 "<strong>{raw_material.name}</strong>" (序列: {raw_material.index}  | 供应商: {raw_material.supplier.name} | 内部编号: {raw_material.internal_number} ) 吗？此操作无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={isDeleting} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                删除中...
              </>
            ) : (
              '确认删除'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
} 