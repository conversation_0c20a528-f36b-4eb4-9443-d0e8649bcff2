"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useEffect, useState, useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormDatePickFiled, FormTextAreaField, FromInputField } from "../share/form/form-filed";
import { FormSearchSelectField } from "../share/form/simple-form";
import { FormImageUploader } from "@/components/share/form/form-image-uploader";
import { CustomerSearchSelect } from "@/components/customers";
import { FullCustomerInfo, CustomerType } from "@/api/customer/types";
import { toast } from "sonner";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, AlertCircle, AlertTriangle } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";

// 定义表单验证模式
const formSchema = z.object({
  // === 项目基本信息 ===
  projectCode: z.string().min(1, "项目编号不能为空"),
  creatorId: z.string().min(1, "录入人不能为空"),
  createDate: z.string().min(1, "创建日期不能为空"),
  projectName: z.string().min(1, "项目名称不能为空"),
  projectTypeId: z.string().min(1, "项目类型不能为空"),
  projectStageId: z.string().min(1, "项目阶段不能为空"),
  projectRemarks: z.string().optional(), // 项目备注
  mainContractor: z.string().optional(), // 总包
  intermediary: z.string().optional(), // 中间人
  expectedSignDate: z.string().min(1, "预计签约日期不能为空"),
  contractCode: z.string().optional(),

  // === 成交几率计算 ===
  businessRelationship: z.string().min(1, "商务关系运营不能为空"), // 商务关系运营
  competitorComparisons: z.array(z.string()).max(4, "最多选择4个竞品比对").default([]), // 竞品比对（多选，最多4个）
  competitorBrand: z.string().optional(), // 竞争品牌
  successProbability: z.number().min(0).max(100).optional().default(0), // 成交机率（计算）

  // === 客户及对接信息 ===
  companyName: z.string().min(1, "客户公司法定名称不能为空"),
  customerProjectManagerId: z.string().min(1, "客户项目经理不能为空"),
  salesId: z.string().min(1, "对接销售不能为空"),
  customerTypeId: z.string().min(1, "客户类型不能为空"),
  customerPhone: z.string().min(1, "客户电话不能为空"),
  importanceLevel: z.string().min(1, "重要程度不能为空"),
  contactPhone: z.string().min(1, "联系人电话不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址").min(1, "联系人邮箱不能为空"),

  // === 产品及交付信息 ===
  totalUnits: z.string().optional(), // 总台数
  elevatorTypes: z.array(z.string()).default([]), // 电梯类型（多选）
  estimatedBudget: z.string().optional(), // 预计设备金额预算
  competitorBrands: z.string().optional(), // 竞品品牌（文字输入）
  expectedDeliveryTime: z.string().optional(), // 预期交货时间
  projectLocationId: z.string().min(1, "项目所在地不能为空"),
  projectAddress: z.string().optional(),
  regionId: z.string().min(1, "所属区域不能为空"),
  isCrossRegion: z.string().min(1, "是否跨区不能为空"),

  // === 项目详情 ===
  projectDescription: z.string().optional(),
  customerRequirements: z.string().optional(),
  remarks: z.string().optional(),
  projectImages: z.array(z.string().url()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface OpportunityFormProps {
  onSubmit?: (values: FormValues) => void;
  defaultValues?: Partial<FormValues>;
  isSubmitting?: boolean;
}

// 初始化本地存储
const initLocalStorage = () => {
  // 录入人（系统内员工）
  if (!localStorage.getItem("creators")) {
    localStorage.setItem(
      "creators",
      JSON.stringify([
        { id: "1", name: "张三" },
        { id: "2", name: "李四" },
        { id: "3", name: "王五" },
        { id: "4", name: "赵六" },
      ])
    );
  }

  // 项目类型
  if (!localStorage.getItem("projectTypes")) {
    localStorage.setItem(
      "projectTypes",
      JSON.stringify([
        { id: "1", name: "新梯项目" },
        { id: "2", name: "旧梯改造" },
        { id: "3", name: "更新项目" },
      ])
    );
  }

  // 项目阶段
  if (!localStorage.getItem("projectStages")) {
    localStorage.setItem(
      "projectStages",
      JSON.stringify([
        { id: "1", name: "規劃階段" },
        { id: "2", name: "方案設計" },
        { id: "3", name: "提交選型" },
        { id: "4", name: "等待報價審批" },
        { id: "5", name: "提交保價" },
        { id: "6", name: "簽訂合同" },
      ])
    );
  }

  // 客户项目经理
  if (!localStorage.getItem("customerProjectManagers")) {
    localStorage.setItem(
      "customerProjectManagers",
      JSON.stringify([
        { id: "1", name: "陈经理" },
        { id: "2", name: "刘经理" },
        { id: "3", name: "王经理" },
        { id: "4", name: "林经理" },
      ])
    );
  }

  // 对接销售
  if (!localStorage.getItem("salesPersons")) {
    localStorage.setItem(
      "salesPersons",
      JSON.stringify([
        { id: "1", name: "赵一" },
        { id: "2", name: "钱二" },
        { id: "3", name: "孙三" },
        { id: "4", name: "李四" },
      ])
    );
  }

  // 客户类型
  if (!localStorage.getItem("customerTypes")) {
    localStorage.setItem(
      "customerTypes",
      JSON.stringify([
        { id: "1", name: "业主" },
        { id: "2", name: "总包" },
        { id: "3", name: "中间人" },
      ])
    );
  }

  // 重要程度
  if (!localStorage.getItem("importanceLevels")) {
    localStorage.setItem(
      "importanceLevels",
      JSON.stringify([
        { id: "A", name: "A级" },
        { id: "B", name: "B级" },
        { id: "C", name: "C级" },
      ])
    );
  }

  // 电梯类型
  if (!localStorage.getItem("elevatorTypes")) {
    localStorage.setItem(
      "elevatorTypes",
      JSON.stringify([
        { id: "1", name: "乘客电梯" },
        { id: "2", name: "货梯" },
        { id: "3", name: "医用电梯" },
        { id: "4", name: "观光电梯" },
        { id: "5", name: "汽车电梯" },
        { id: "6", name: "家用电梯" },
        { id: "7", name: "消防员电梯" },
      ])
    );
  }

  // 项目所在地（越南主要城市和省份）- 强制更新
  localStorage.setItem(
    "locations",
    JSON.stringify([
      // 北部地区
      { id: "1", name: "河內市 (Hà Nội)" },
      { id: "2", name: "海防市 (Hải Phòng)" },
      { id: "3", name: "廣寧省 (Quảng Ninh)" },
      { id: "4", name: "北寧省 (Bắc Ninh)" },
      { id: "5", name: "太原省 (Thái Nguyên)" },
      { id: "6", name: "永福省 (Vĩnh Phúc)" },
      { id: "7", name: "河南省 (Hà Nam)" },
      { id: "8", name: "南定省 (Nam Định)" },
      
      // 中部地区
      { id: "9", name: "岘港市 (Đà Nẵng)" },
      { id: "10", name: "順化市 (Huế)" },
      { id: "11", name: "廣南省 (Quảng Nam)" },
      { id: "12", name: "廣義省 (Quảng Ngãi)" },
      { id: "13", name: "平定省 (Bình Định)" },
      { id: "14", name: "富安省 (Phú Yên)" },
      { id: "15", name: "慶和省 (Khánh Hòa)" },
      { id: "16", name: "寧順省 (Ninh Thuận)" },
      { id: "17", name: "平順省 (Bình Thuận)" },
      
      // 南部地区
      { id: "18", name: "胡志明市 (TP. Hồ Chí Minh)" },
      { id: "19", name: "平陽省 (Bình Dương)" },
      { id: "20", name: "同奈省 (Đồng Nai)" },
      { id: "21", name: "巴地頭頓省 (Bà Rịa - Vũng Tàu)" },
      { id: "22", name: "西寧省 (Tây Ninh)" },
      { id: "23", name: "隆安省 (Long An)" },
      { id: "24", name: "前江省 (Tiền Giang)" },
      { id: "25", name: "檳椥省 (Bến Tre)" },
      { id: "26", name: "芹苴市 (Cần Thơ)" },
      { id: "27", name: "安江省 (An Giang)" },
      { id: "28", name: "同塔省 (Đồng Tháp)" },
      { id: "29", name: "薄遼省 (Bạc Liêu)" },
      { id: "30", name: "金甌省 (Cà Mau)" },
    ])
  );

  // 所属区域（越南三大地理区域）- 强制更新
  localStorage.setItem(
    "regions",
    JSON.stringify([
      { id: "1", name: "北部地區 (Miền Bắc)" },
      { id: "2", name: "中部地區 (Miền Trung)" },
      { id: "3", name: "南部地區 (Miền Nam)" },
      { id: "4", name: "岘港經濟區 (Vùng Kinh tế Đà Nẵng)" },
    ])
  );

  // 是否跨区
  if (!localStorage.getItem("crossRegionOptions")) {
    localStorage.setItem(
      "crossRegionOptions",
      JSON.stringify([
        { id: "1", name: "是" },
        { id: "2", name: "否" },
      ])
    );
  }

  // 商务关系运营
  if (!localStorage.getItem("businessRelationships")) {
    localStorage.setItem(
      "businessRelationships",
      JSON.stringify([
        { id: "1", name: "切入业主 (Contacting owner)", probability: 5 },
        { id: "2", name: "切入总包 (Contacting maincon)", probability: 5 },
        { id: "3", name: "切入业主及总包 (Contacting owner&maincon)", probability: 10 },
        { id: "4", name: "拉拢业主 (Convince owner)", probability: 35 },
        { id: "5", name: "拉拢总包 (Convince maincon)", probability: 15 },
        { id: "6", name: "拉拢业主及总包 (Convince owner& maincon)", probability: 50 },
        { id: "7", name: "未运营 (Null)", probability: 0 },
      ])
    );
  }

  // 竞品比对选项
  if (!localStorage.getItem("competitorComparisonOptions")) {
    localStorage.setItem(
      "competitorComparisonOptions",
      JSON.stringify([
        { id: "1", name: "成本优势 (Price leading)", probability: 20 },
        { id: "2", name: "成本劣势 (Price behind)", probability: 0 },
        { id: "3", name: "服务优势 (Service leading)", probability: 10 },
        { id: "4", name: "服务劣势 (Service behind)", probability: 0 },
        { id: "5", name: "品牌优势 (Brand leading)", probability: 15 },
        { id: "6", name: "品牌劣势 (Brand behind)", probability: 0 },
        { id: "7", name: "交期优势 (Duration leading)", probability: 5 },
        { id: "8", name: "交期劣势 (Duration behind)", probability: 0 },
        { id: "9", name: "未知 (Null)", probability: 0 },
      ])
    );
  }

  // 初始化默认商机数据
  if (!localStorage.getItem("opportunities")) {
    const today = new Date();
    const nextMonth = new Date();
    nextMonth.setMonth(today.getMonth() + 1);

    localStorage.setItem(
      "opportunities",
      JSON.stringify([
        {
          id: "1001",
          projectCode: "ELV-2025-1001",
          projectName: "万科城市之光",
          customerName: "万科地产",
          projectManagerName: "张三",
          salesName: "赵一",
          progress: 75,
          status: "合同录入",
          createdAt: today.toISOString(),
          expectedSignDate: nextMonth.toISOString(),
          estimatedAmount: "380",
          elevatorCount: "12",
          projectTypeName: "新梯项目",
          customerTypeName: "业主",
          locationName: "胡志明市 (TP. Hồ Chí Minh)",
          projectAddress: "胡志明市第一郡阮惠大街1000号",
          engineeringTypeName: "新建项目",
          industryTypeName: "商业地产",
          contactPhone: "13912345678",
          contactEmail: "<EMAIL>",
          successProbability: 75,
          projectStatus: "已签约",
          statusUpdatedAt: today.toISOString(),
        },
      ])
    );
  }
};

/**
 * 商机创建表单
 */
export function DesignTaskForm({
  onSubmit,
  defaultValues = {},
  isSubmitting = false,
}: OpportunityFormProps) {
  const router = useRouter();
  const [creators, setCreators] = useState<{ id: string; name: string }[]>([]);
  const [projectTypes, setProjectTypes] = useState<{ id: string; name: string }[]>([]);
  const [projectStages, setProjectStages] = useState<{ id: string; name: string }[]>([]);
  const [customerProjectManagers, setCustomerProjectManagers] = useState<{ id: string; name: string }[]>([]);
  const [salesPersons, setSalesPersons] = useState<{ id: string; name: string }[]>([]);
  const [customerTypes, setCustomerTypes] = useState<{ id: string; name: string }[]>([]);
  const [importanceLevels, setImportanceLevels] = useState<{ id: string; name: string }[]>([]);
  const [elevatorTypes, setElevatorTypes] = useState<{ id: string; name: string }[]>([]);
  const [locations, setLocations] = useState<{ id: string; name: string }[]>([]);
  const [regions, setRegions] = useState<{ id: string; name: string }[]>([]);
  const [crossRegionOptions, setCrossRegionOptions] = useState<{ id: string; name: string }[]>([]);
  const [businessRelationships, setBusinessRelationships] = useState<{ id: string; name: string; probability: number }[]>([]);
  const [competitorComparisonOptions, setCompetitorComparisonOptions] = useState<{ id: string; name: string; probability: number }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedElevatorTypes, setSelectedElevatorTypes] = useState<string[]>([]);

  // 初始化数据
  useEffect(() => {
    initLocalStorage();
    
    // 加载数据
    setCreators(JSON.parse(localStorage.getItem("creators") || "[]"));
    setProjectTypes(JSON.parse(localStorage.getItem("projectTypes") || "[]"));
    setProjectStages(JSON.parse(localStorage.getItem("projectStages") || "[]"));
    setCustomerProjectManagers(JSON.parse(localStorage.getItem("customerProjectManagers") || "[]"));
    setSalesPersons(JSON.parse(localStorage.getItem("salesPersons") || "[]"));
    setCustomerTypes(JSON.parse(localStorage.getItem("customerTypes") || "[]"));
    setImportanceLevels(JSON.parse(localStorage.getItem("importanceLevels") || "[]"));
    setElevatorTypes(JSON.parse(localStorage.getItem("elevatorTypes") || "[]"));
    setLocations(JSON.parse(localStorage.getItem("locations") || "[]"));
    setRegions(JSON.parse(localStorage.getItem("regions") || "[]"));
    setCrossRegionOptions(JSON.parse(localStorage.getItem("crossRegionOptions") || "[]"));
    setBusinessRelationships(JSON.parse(localStorage.getItem("businessRelationships") || "[]"));
    setCompetitorComparisonOptions(JSON.parse(localStorage.getItem("competitorComparisonOptions") || "[]"));
  }, []);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      projectCode:
        defaultValues.projectCode ||
        "ELV-" +
          new Date().getFullYear().toString() +
          "-" +
          Math.floor(1000 + Math.random() * 9000),
      creatorId: defaultValues.creatorId || "1", // 默认创建者
      createDate: defaultValues.createDate || new Date().toISOString().split("T")[0],
      projectName: defaultValues.projectName || "",
      projectTypeId: defaultValues.projectTypeId || "",
      projectStageId: defaultValues.projectStageId || "",
      projectRemarks: defaultValues.projectRemarks || "",
      mainContractor: defaultValues.mainContractor || "",
      intermediary: defaultValues.intermediary || "",
      expectedSignDate: defaultValues.expectedSignDate || "",
      contractCode: defaultValues.contractCode || "",
      businessRelationship: defaultValues.businessRelationship || "",
      competitorComparisons: defaultValues.competitorComparisons || [],
      competitorBrand: defaultValues.competitorBrand || "",
      successProbability: defaultValues.successProbability ?? 0,
      companyName: defaultValues.companyName || "",
      customerProjectManagerId: defaultValues.customerProjectManagerId || "",
      salesId: defaultValues.salesId || "",
      customerTypeId: defaultValues.customerTypeId || "",
      customerPhone: defaultValues.customerPhone || "",
      importanceLevel: defaultValues.importanceLevel || "",
      contactPhone: defaultValues.contactPhone || "",
      contactEmail: defaultValues.contactEmail || "",
      totalUnits: defaultValues.totalUnits || "",
      elevatorTypes: defaultValues.elevatorTypes || [],
      estimatedBudget: defaultValues.estimatedBudget || "",
      competitorBrands: defaultValues.competitorBrands || "",
      expectedDeliveryTime: defaultValues.expectedDeliveryTime || "",
      projectLocationId: defaultValues.projectLocationId || "",
      projectAddress: defaultValues.projectAddress || "",
      regionId: defaultValues.regionId || "",
      isCrossRegion: defaultValues.isCrossRegion || "2", // 默认否
      projectDescription: defaultValues.projectDescription || "",
      customerRequirements: defaultValues.customerRequirements || "",
      remarks: defaultValues.remarks || "",
      projectImages: defaultValues.projectImages || [],
    },
  });

  // 计算成交机率（基于商务关系运营和竞品比对）
  const calculateSuccessProbability = useCallback((businessRelationshipId?: string, competitorComparisonIds?: string[]): number => {
    const brId = businessRelationshipId || form.getValues("businessRelationship");
    const ccIds = competitorComparisonIds || form.getValues("competitorComparisons") || [];

    // 获取商务关系运营的概率
    const businessRelationship = businessRelationships.find(br => br.id === brId);
    const businessProbability = businessRelationship?.probability || 0;

    // 获取竞品比对的概率（多个选择叠加）
    const competitorProbabilities = ccIds.map(id => {
      const option = competitorComparisonOptions.find(opt => opt.id === id);
      return option?.probability || 0;
    });
    const totalCompetitorProbability = competitorProbabilities.reduce((sum, prob) => sum + prob, 0);

    // 计算总概率：商务关系概率 + 竞品比对叠加概率
    const totalProbability = businessProbability + totalCompetitorProbability;
    
    // 确保概率在0-100之间
    return Math.min(100, Math.max(0, totalProbability));
  }, [businessRelationships, competitorComparisonOptions]);

  // 处理选择客户
  const handleSelectCustomer = (customer: FullCustomerInfo) => {
    // 获取客户显示名称
    const getCustomerDisplayName = (customer: FullCustomerInfo) => {
      switch (customer.customer_type) {
        case CustomerType.OWNER:
          return customer.owner_info?.owner_name || customer.name
        case CustomerType.MAINCONTRACTOR:
          return customer.maincontractor_info?.company_name || customer.name
        case CustomerType.BROKER:
          return customer.broker_info?.broker_name || customer.name
        default:
          return customer.name
      }
    }

    // 获取客户电话
    const getCustomerPhone = (customer: FullCustomerInfo) => {
      switch (customer.customer_type) {
        case CustomerType.OWNER:
          return customer.owner_info?.owner_phone || ''
        case CustomerType.MAINCONTRACTOR:
          return customer.maincontractor_info?.company_phone || ''
        case CustomerType.BROKER:
          return customer.broker_info?.broker_phone || ''
        default:
          return ''
      }
    }

    // 获取客户邮箱
    const getCustomerEmail = (customer: FullCustomerInfo) => {
      switch (customer.customer_type) {
        case CustomerType.OWNER:
          return customer.owner_info?.email || ''
        case CustomerType.MAINCONTRACTOR:
          return customer.maincontractor_info?.company_email || ''
        case CustomerType.BROKER:
          return customer.broker_info?.broker_email || ''
        default:
          return ''
      }
    }

    // 获取客户的第一个联系人电话
    const getContactPhone = (customer: FullCustomerInfo) => {
      let contacts: Array<{name: string, phone: string}> = []
      switch (customer.customer_type) {
        case CustomerType.OWNER:
          contacts = customer.owner_info?.contacts || []
          break
        case CustomerType.MAINCONTRACTOR:
          contacts = customer.maincontractor_info?.contacts || []
          break
        default:
          return ''
      }
      return contacts.length > 0 ? contacts[0].phone : ''
    }

    // 映射客户类型到表单选项
    const getCustomerTypeId = (customerType: CustomerType) => {
      // 这里需要根据实际的customerTypes选项进行映射
      // 假设customerTypes中的id对应关系为：
      const typeMapping: Record<CustomerType, string> = {
        [CustomerType.OWNER]: '1',        // 业主
        [CustomerType.MAINCONTRACTOR]: '2', // 总包
        [CustomerType.BROKER]: '3',       // 中间人
      }
      return typeMapping[customerType] || '1'
    }

    // 映射重要程度到表单选项
    const getImportanceLevelId = (customer: FullCustomerInfo) => {
      let importanceLevel = ''
      switch (customer.customer_type) {
        case CustomerType.OWNER:
          importanceLevel = customer.owner_info?.importance_level || ''
          break
        case CustomerType.MAINCONTRACTOR:
          importanceLevel = customer.maincontractor_info?.importance_level || ''
          break
        case CustomerType.BROKER:
          importanceLevel = customer.broker_info?.importance_level || ''
          break
      }
      
      // 映射重要程度到表单选项ID
      const levelMapping: Record<string, string> = {
        'A': '1',
        'B': '2', 
        'C': '3',
      }
      return levelMapping[importanceLevel] || '2' // 默认B级
    }

    // 自动填入表单字段
    form.setValue("companyName", getCustomerDisplayName(customer))
    form.setValue("customerPhone", getCustomerPhone(customer))
    form.setValue("contactEmail", getCustomerEmail(customer))
    form.setValue("customerTypeId", getCustomerTypeId(customer.customer_type))
    form.setValue("importanceLevel", getImportanceLevelId(customer))
    
    // 如果有联系人电话，也填入（联系人电话和客户电话可能不同）
    const contactPhone = getContactPhone(customer)
    if (contactPhone) {
      form.setValue("contactPhone", contactPhone)
    } else {
      // 如果没有联系人电话，使用客户电话
      form.setValue("contactPhone", getCustomerPhone(customer))
    }
  };

  const handleSubmit = (values: FormValues) => {
    setIsLoading(true);

    try {
      // 获取现有商机
      const existingOpportunities = JSON.parse(localStorage.getItem("opportunities") || "[]");

      // 创建新商机对象
      const newOpportunity = {
        id: uuidv4(),
        ...values,
        status: "商机获取",
        progress: 10,
        createdAt: new Date().toISOString(),
        creatorName: creators.find(c => c.id === values.creatorId)?.name || "",
        projectTypeName: projectTypes.find(p => p.id === values.projectTypeId)?.name || "",
        projectStageName: projectStages.find(p => p.id === values.projectStageId)?.name || "",
        customerProjectManagerName: customerProjectManagers.find(p => p.id === values.customerProjectManagerId)?.name || "",
        salesName: salesPersons.find(s => s.id === values.salesId)?.name || "",
        customerTypeName: customerTypes.find(c => c.id === values.customerTypeId)?.name || "",
        importanceLevelName: importanceLevels.find(i => i.id === values.importanceLevel)?.name || "",
        elevatorTypesNames: values.elevatorTypes?.map(typeId => 
          elevatorTypes.find(e => e.id === typeId)?.name || ""
        ).filter(Boolean).join(", ") || "",
        locationName: locations.find(l => l.id === values.projectLocationId)?.name || "",
        regionName: regions.find(r => r.id === values.regionId)?.name || "",
        isCrossRegionName: crossRegionOptions.find(i => i.id === values.isCrossRegion)?.name || "否",
      };

      // 添加到商机列表
      existingOpportunities.push(newOpportunity);

      // 保存回localStorage
      localStorage.setItem("opportunities", JSON.stringify(existingOpportunities));

      toast.success("商机创建成功！");

      // 如果有自定义的提交处理函数，调用它
      if (onSubmit) {
        onSubmit(values);
      } else {
        // 否则导航到dashboard
        router.push("/dashboard");
      }
    } catch (error) {
      console.error("保存商机失败:", error);
      toast.error("商机创建失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  // 处理电梯类型多选
  const handleElevatorTypeChange = (typeId: string, checked: boolean) => {
    const currentTypes = form.getValues("elevatorTypes") || [];
    if (checked) {
      form.setValue("elevatorTypes", [...currentTypes, typeId]);
    } else {
      form.setValue("elevatorTypes", currentTypes.filter(id => id !== typeId));
    }
  };

  // 处理竞品比对多选（最多4个）
  const handleCompetitorComparisonChange = (comparisonId: string, checked: boolean) => {
    const currentComparisons = form.getValues("competitorComparisons") || [];
    let newComparisons: string[];
    
    if (checked) {
      if (currentComparisons.length >= 4) {
        toast.error("最多只能选择4个竞品比对选项");
        return;
      }
      newComparisons = [...currentComparisons, comparisonId];
    } else {
      newComparisons = currentComparisons.filter(id => id !== comparisonId);
    }
    
    // 设置新的竞品比对选择
    form.setValue("competitorComparisons", newComparisons);
    
    // 重新计算成交概率
    const businessRelationshipId = form.getValues("businessRelationship");
    const probability = calculateSuccessProbability(businessRelationshipId, newComparisons);
    form.setValue("successProbability", probability);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* 项目基本信息 */}
        <Card className="shadow-sm">
          <CardHeader className="px-4 md:px-6 pb-2 flex flex-row items-center justify-between">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              项目基本信息
            </CardTitle>
            <div className="text-xs text-muted-foreground">
              <span className="text-red-500">*</span> 表示必填项
            </div>
          </CardHeader>
          <CardContent className="px-4 md:px-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FromInputField
                title="项目编号"
                placeholder="系统自动生成"
                form={form}
                name="projectCode"
                disabled={true}
              />
              <FormSearchSelectField
                label="录入人"
                form={form}
                name="creatorId"
                isLoading={isLoading}
                filteredItems={creators}
                required
              />
              <FormDatePickFiled label="创建日期" form={form} name="createDate" required />
              
              <FromInputField
                title="项目名称"
                placeholder="请输入项目名称"
                form={form}
                name="projectName"
                required
              />
              
              <FromInputField
                title="总包"
                placeholder="请输入总包信息"
                form={form}
                name="mainContractor"
              />
              
              <FromInputField
                title="中间人"
                placeholder="请输入中间人信息"
                form={form}
                name="intermediary"
              />              
              {/* <FormTextAreaField
                form={form}
                label="项目备注"
                name="projectRemarks"
              /> */}
              <FormSearchSelectField
                form={form}
                label="项目类型"
                name="projectTypeId"
                isLoading={isLoading}
                filteredItems={projectTypes}
                required
              />
              {/* <FormSearchSelectField
                form={form}
                label="项目阶段"
                name="projectStageId"
                isLoading={isLoading}
                filteredItems={projectStages}
                required
              /> */}
              <FormDatePickFiled
                label="预计签约日期"
                form={form}
                name="expectedSignDate"
                required
              />
              {/* <FromInputField
                title="合同编号"
                placeholder="请输入合同编号"
                form={form}
                name="contractCode"
              /> */}
                            <FormSearchSelectField
                form={form}
                label="项目所在地"
                name="projectLocationId"
                isLoading={isLoading}
                filteredItems={locations}
                required
              />
              
              <FromInputField
                title="项目地址"
                placeholder="请输入详细地址"
                form={form}
                name="projectAddress"
              />
              <FormSearchSelectField
                form={form}
                label="所属区域"
                name="regionId"
                isLoading={isLoading}
                filteredItems={regions}
                required
              />
              <FormSearchSelectField
                form={form}
                label="是否跨区"
                name="isCrossRegion"
                isLoading={isLoading}
                filteredItems={crossRegionOptions}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* 成交几率计算 */}
        <Card className="shadow-sm">
          <CardHeader className="px-4 md:px-6 pb-2">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              商机成交几率
            </CardTitle>
          </CardHeader>
          <CardContent className="px-4 md:px-6">
            <div className="space-y-4">
                             {/* 商务关系运营 */}
               <div className="grid gap-2">
                 <label className="text-sm font-medium">
                   商务关系运营 <span className="text-red-500">*</span>
                 </label>
                 <FormField
                   control={form.control}
                   name="businessRelationship"
                   render={({ field }) => (
                     <FormItem>
                       <FormControl>
                         <select
                           {...field}
                           className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                           onChange={(e) => {
                             field.onChange(e);
                             // 重新计算成交概率
                             const competitorComparisonIds = form.getValues("competitorComparisons") || [];
                             const probability = calculateSuccessProbability(e.target.value, competitorComparisonIds);
                             form.setValue("successProbability", probability);
                           }}
                         >
                           <option value="">请选择商务关系运营</option>
                           {businessRelationships.map((item) => (
                             <option key={item.id} value={item.id}>
                               {item.name}
                             </option>
                           ))}
                         </select>
                       </FormControl>
                       <FormMessage />
                     </FormItem>
                   )}
                 />
                              </div>
               
               {/* 竞争品牌 */}
               <FromInputField
                 title="竞争品牌"
                 placeholder="请输入竞争品牌"
                 form={form}
                 name="competitorBrand"
               />
               
               {/* 竞品比对（多选，最多4个） */}
               <div className="grid gap-2">
                <label className="text-sm font-medium">
                  竞品比对 <span className="text-red-500">*</span>
                  <span className="text-xs text-muted-foreground ml-2">
                    （多选，最多选择4个）
                  </span>
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 p-4 border rounded-lg">
                  {competitorComparisonOptions.map((option) => (
                    <FormField
                      key={option.id}
                      control={form.control}
                      name="competitorComparisons"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(option.id) || false}
                              onCheckedChange={(checked) => {
                                handleCompetitorComparisonChange(option.id, checked as boolean);
                              }}
                            />
                          </FormControl>
                          <FormLabel className="text-sm font-normal">
                            {option.name}
                          </FormLabel>
                        </FormItem>
                      )}
                    />
                  ))}
                </div>
              </div>
              
              {/* 成交几率显示 */}
              <div className="grid gap-2">
                <label className="text-sm font-medium">成交机率</label>
                <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 border rounded-lg">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="px-4 py-2 text-lg font-bold">
                      {form.watch("successProbability")}%
                    </Badge>
                    <span className="text-sm text-muted-foreground">（基于商务关系和竞品比对自动计算）</span>
                  </div>
                </div>
                {/* <div className="text-xs text-muted-foreground space-y-1">
                  <p>• 计算规则：商务关系运营概率 + 竞品比对最高概率</p>
                  <p>• 商务关系运营概率范围：0% - 50%</p>
                  <p>• 竞品比对概率范围：0% - 20%</p>
                </div> */}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 客户及对接信息 */}
        <Card className="shadow-sm">
          <CardHeader className="px-4 md:px-6 pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold">客户及对接信息</CardTitle>
              <CustomerSearchSelect onSelectCustomer={handleSelectCustomer} />
            </div>
          </CardHeader>
          <CardContent className="px-4 md:px-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FromInputField
                title="客户公司法定名称"
                placeholder="请输入客户公司名称"
                form={form}
                name="companyName"
                required
              />
              <FormSearchSelectField
                label="客户项目经理"
                form={form}
                name="customerProjectManagerId"
                isLoading={isLoading}
                filteredItems={customerProjectManagers}
                required
              />
              <FormSearchSelectField
                label="对接销售"
                form={form}
                name="salesId"
                isLoading={isLoading}
                filteredItems={salesPersons}
                required
              />
              
              <FormSearchSelectField
                label="客户类型"
                form={form}
                name="customerTypeId"
                isLoading={isLoading}
                filteredItems={customerTypes}
                required
              />
              <FromInputField
                title="客户电话"
                placeholder="请输入客户电话"
                form={form}
                name="customerPhone"
                required
              />
              <FormSearchSelectField
                label="重要程度"
                form={form}
                name="importanceLevel"
                isLoading={isLoading}
                filteredItems={importanceLevels}
                required
              />
              
              <FromInputField
                title="联系人电话"
                placeholder="请输入联系人电话"
                form={form}
                name="contactPhone"
                required
              />
              <FromInputField
                title="联系人邮箱"
                placeholder="请输入联系人邮箱"
                form={form}
                name="contactEmail"
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* 产品及交付信息 */}
        <Card className="shadow-sm">
          <CardHeader className="px-4 md:px-6 pb-2">
            <CardTitle className="text-xl font-bold">产品及交付信息</CardTitle>
          </CardHeader>
          <CardContent className="px-4 md:px-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <FromInputField
                title="总台数"
                placeholder="请输入电梯总台数"
                form={form}
                name="totalUnits"
              />
              <FromInputField
                title="预计设备金额预算"
                placeholder="请输入预算金额（万元）"
                form={form}
                name="estimatedBudget"
              />
              <FromInputField
                title="预期交货时间"
                placeholder="请输入预期交货时间"
                form={form}
                name="expectedDeliveryTime"
              />
            </div>
            
            {/* 电梯类型（多选） */}
            <div className="grid gap-2">
              <label className="text-sm font-medium">电梯类型（多选）</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 p-4 border rounded-lg">
                {elevatorTypes.map((type) => (
                  <FormField
                    key={type.id}
                    control={form.control}
                    name="elevatorTypes"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(type.id) || false}
                            onCheckedChange={(checked) => {
                              handleElevatorTypeChange(type.id, checked as boolean);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-normal">
                          {type.name}
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 项目详情 */}
        <Card className="shadow-sm">
          <CardHeader className="px-4 md:px-6">
            <CardTitle className="text-lg font-semibold">项目详情</CardTitle>
          </CardHeader>
          <CardContent className="px-4 md:px-6">
            <div className="grid grid-cols-1 gap-4">
              <FormTextAreaField form={form} label="项目描述" name="projectDescription" />
              <FormTextAreaField form={form} label="客户需求" name="customerRequirements" />
              <FormTextAreaField form={form} label="备注" name="remarks" />
              {/* 项目图片上传 */}
              <FormImageUploader
                form={form}
                label="项目图片"
                name="projectImages"
                maxImages={10}
                acceptedFileTypes="image/*"
                maxFileSize={5}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end px-2">
          <Button type="button" variant="outline" className="mr-2" onClick={() => router.back()}>
            取消
          </Button>
          <Button type="submit" disabled={isLoading || isSubmitting}>
            {isSubmitting ? "提交中..." : "创建商机"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
