"use client"

import React from "react";
import { cn } from "@/lib/utils";

interface AppleLoadingProps {
  size?: "sm" | "md" | "lg" | "xl";
  title?: string;
  description?: string;
  className?: string;
  showProgress?: boolean;
  fullScreen?: boolean;
}

const sizeConfig = {
  sm: {
    spinner: "w-8 h-8",
    title: "text-sm",
    description: "text-xs",
    spacing: "space-y-3",
  },
  md: {
    spinner: "w-12 h-12",
    title: "text-lg",
    description: "text-sm",
    spacing: "space-y-4",
  },
  lg: {
    spinner: "w-16 h-16",
    title: "text-xl",
    description: "text-sm",
    spacing: "space-y-6",
  },
  xl: {
    spinner: "w-20 h-20",
    title: "text-2xl",
    description: "text-base",
    spacing: "space-y-8",
  },
};

export function AppleLoading({
  size = "md",
  title,
  description,
  className,
  showProgress = false,
  fullScreen = false,
}: AppleLoadingProps) {
  const config = sizeConfig[size];

  const content = (
    <div className={cn("text-center", config.spacing, className)}>
      {/* 苹果风格的loading动画 */}
      <div className="relative">
        <div className={cn("mx-auto", config.spinner)}>
          <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
          <div className="absolute inset-0 rounded-full border-4 border-[#00B4AA] border-t-transparent animate-spin"></div>
        </div>
      </div>

      {/* Loading文本 */}
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h2 className={cn("font-semibold text-gray-800", config.title)}>
              {title}
            </h2>
          )}
          {description && (
            <p className={cn("text-gray-600", config.description)}>
              {description}
            </p>
          )}
        </div>
      )}

      {/* 进度指示器 */}
      {showProgress && (
        <div className="w-64 mx-auto">
          <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
            <div className="h-full bg-gradient-to-r from-[#00B4AA] to-[#0092D8] rounded-full animate-pulse"></div>
          </div>
        </div>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        {content}
      </div>
    );
  }

  return content;
}

// 简化的loading组件，用于替换现有的简单loading
export function SimpleAppleLoading({
  className,
  text = "加载中...",
}: {
  className?: string;
  text?: string;
}) {
  return (
    <div className={cn("flex items-center justify-center min-h-[60vh]", className)}>
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="w-10 h-10 mx-auto">
            <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-4 border-[#00B4AA] border-t-transparent animate-spin"></div>
          </div>
        </div>
        <p className="text-sm text-gray-600">{text}</p>
      </div>
    </div>
  );
}

// 页面级loading组件
export function PageLoading({
  title = "加载中...",
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center gap-4">
        <div className="relative">
          <div className="w-12 h-12 mx-auto">
            <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-4 border-[#00B4AA] border-t-transparent animate-spin"></div>
          </div>
        </div>
        <div className="text-center space-y-2">
          <p className="text-lg font-medium text-gray-800">{title}</p>
          {description && (
            <p className="text-sm text-gray-600">{description}</p>
          )}
        </div>
      </div>
    </div>
  );
}
