"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { IconEdit, IconTrash, IconPlus, IconDeviceFloppy, IconCheck, IconX, IconFilter } from '@tabler/icons-react';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface LanguageDict {
  [key: string]: any;
}

const languages = [
  { code: 'zh', name: '简体中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
];

const moduleDescriptions: Record<string, string> = {
  auth: '用户认证相关的翻译内容',
  system: '系统设置和配置相关的翻译',
  common: '通用的翻译内容，如按钮、标签等',
};

export function LanguageManagement() {
  const [dictionaries, setDictionaries] = useState<Record<string, LanguageDict>>({});
  const [originalDictionaries, setOriginalDictionaries] = useState<Record<string, LanguageDict>>({});
  const [loading, setLoading] = useState(true);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [newKey, setNewKey] = useState('');
  const [newValues, setNewValues] = useState<Record<string, string>>({});
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isAddModuleDialogOpen, setIsAddModuleDialogOpen] = useState(false);
  const [selectedModule, setSelectedModule] = useState<string>('all');
  const [newModuleName, setNewModuleName] = useState('');
  const [newModuleDesc, setNewModuleDesc] = useState('');
  const [editingValues, setEditingValues] = useState<Record<string, Record<string, string>>>({});

  // 模拟加载语言字典数据
  useEffect(() => {
    const loadDictionaries = async () => {
      try {
        // 这里可以替换为实际的API调用
        const mockData = {
          zh: {
            "auth.welcome_to_use": "欢迎使用 Midea CRM.",
            "auth.login_success": "登录成功",
            "auth.redirecting": "即将跳转至首页...",
            "auth.login_failed": "登录失败",
            "auth.check_account_password": "请检查账号密码是否正确",
            "system.settings": "系统设置",
            "system.language_management": "多语言管理"
          },
          en: {
            "auth.welcome_to_use": "Welcome to use Midea CRM.",
            "auth.login_success": "Login successful",
            "auth.redirecting": "Redirecting to home page...",
            "auth.login_failed": "Login failed",
            "auth.check_account_password": "Please check your account and password",
            "system.settings": "System Settings",
            "system.language_management": "Language Management"
          },
          vi: {
            "auth.welcome_to_use": "Chào mừng sử dụng Midea CRM.",
            "auth.login_success": "Đăng nhập thành công",
            "auth.redirecting": "Đang chuyển hướng đến trang chủ...",
            "auth.login_failed": "Đăng nhập thất bại",
            "auth.check_account_password": "Vui lòng kiểm tra tài khoản và mật khẩu",
            "system.settings": "Cài đặt hệ thống",
            "system.language_management": "Quản lý ngôn ngữ"
          }
        };
        setDictionaries(mockData);
        setOriginalDictionaries(JSON.parse(JSON.stringify(mockData)));
      } catch (error) {
        console.error('Failed to load dictionaries:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDictionaries();
  }, []);

  // 获取所有的键
  const getAllKeys = () => {
    const keys = new Set<string>();
    Object.values(dictionaries).forEach(dict => {
      Object.keys(dict).forEach(key => keys.add(key));
    });
    return Array.from(keys).sort();
  };

  // 获取所有模块
  const getAllModules = () => {
    const modules = new Set<string>();
    getAllKeys().forEach(key => {
      const module = key.split('.')[0];
      modules.add(module);
    });
    return Array.from(modules).sort();
  };

  // 根据模块过滤键
  const getFilteredKeys = () => {
    const allKeys = getAllKeys();
    if (selectedModule === 'all') {
      return allKeys;
    }
    return allKeys.filter(key => key.startsWith(selectedModule + '.'));
  };

  // 检查行是否被修改
  const isRowModified = (key: string) => {
    return languages.some(lang => 
      dictionaries[lang.code]?.[key] !== originalDictionaries[lang.code]?.[key]
    );
  };

  // 保存单行
  const saveRow = (key: string) => {
    setOriginalDictionaries(prev => {
      const newOriginal = { ...prev };
      languages.forEach(lang => {
        if (!newOriginal[lang.code]) newOriginal[lang.code] = {};
        newOriginal[lang.code][key] = dictionaries[lang.code]?.[key] || '';
      });
      return newOriginal;
    });
    setEditingKey(null);
    setEditingValues(prev => {
      const newValues = { ...prev };
      delete newValues[key];
      return newValues;
    });
  };

  // 取消单行编辑
  const cancelRowEdit = (key: string) => {
    setDictionaries(prev => {
      const newDict = { ...prev };
      languages.forEach(lang => {
        if (originalDictionaries[lang.code]?.[key] !== undefined) {
          newDict[lang.code][key] = originalDictionaries[lang.code][key];
        }
      });
      return newDict;
    });
    setEditingKey(null);
    setEditingValues(prev => {
      const newValues = { ...prev };
      delete newValues[key];
      return newValues;
    });
  };

  // 更新字典值
  const updateDictionaryValue = (langCode: string, key: string, value: string) => {
    setDictionaries(prev => ({
      ...prev,
      [langCode]: {
        ...prev[langCode],
        [key]: value
      }
    }));
  };

  // 删除字典键
  const deleteDictionaryKey = (key: string) => {
    setDictionaries(prev => {
      const newDict = { ...prev };
      Object.keys(newDict).forEach(langCode => {
        delete newDict[langCode][key];
      });
      return newDict;
    });
  };

  // 添加新的字典键
  const addNewKey = () => {
    if (!newKey.trim()) return;
    
    languages.forEach(lang => {
      updateDictionaryValue(lang.code, newKey, newValues[lang.code] || '');
    });
    
    // 更新原始数据
    setOriginalDictionaries(prev => {
      const newOriginal = { ...prev };
      languages.forEach(lang => {
        if (!newOriginal[lang.code]) newOriginal[lang.code] = {};
        newOriginal[lang.code][newKey] = newValues[lang.code] || '';
      });
      return newOriginal;
    });
    
    setNewKey('');
    setNewValues({});
    setIsAddDialogOpen(false);
  };

  // 添加新模块
  const addNewModule = () => {
    if (!newModuleName.trim()) return;
    
    // 添加模块描述
    if (newModuleDesc.trim()) {
      moduleDescriptions[newModuleName] = newModuleDesc;
    }
    
    const sampleKey = `${newModuleName}.sample`;
    languages.forEach(lang => {
      updateDictionaryValue(lang.code, sampleKey, `Sample ${lang.name} text`);
    });
    
    // 更新原始数据
    setOriginalDictionaries(prev => {
      const newOriginal = { ...prev };
      languages.forEach(lang => {
        if (!newOriginal[lang.code]) newOriginal[lang.code] = {};
        newOriginal[lang.code][sampleKey] = `Sample ${lang.name} text`;
      });
      return newOriginal;
    });
    
    setNewModuleName('');
    setNewModuleDesc('');
    setIsAddModuleDialogOpen(false);
    setSelectedModule(newModuleName);
  };

  // 计算文本差异并高亮显示
  const renderTextDiff = (original: string, current: string) => {
    if (original === current) return current;
    
    // 简单的差异检测和高亮
    const words = current.split('');
    const originalWords = original.split('');
    
    return (
      <div className="space-y-1">
        <div className="text-xs text-gray-500">原文:</div>
        <div className="text-xs bg-red-50 p-1 rounded border-l-2 border-red-200">
          {original}
        </div>
        <div className="text-xs text-gray-500">修改后:</div>
        <div className="text-xs bg-yellow-50 p-1 rounded border-l-2 border-yellow-400">
          <span className="bg-yellow-200 px-1 rounded">{current}</span>
        </div>
      </div>
    );
  };

  // 保存所有修改
  const saveChanges = async () => {
    try {
      // 这里可以添加保存到服务器的逻辑
      console.log('Saving dictionaries:', dictionaries);
      alert('保存成功！');
    } catch (error) {
      console.error('Failed to save dictionaries:', error);
      alert('保存失败！');
    }
  };

  if (loading) {
    return <div className="flex justify-center p-8">加载中...</div>;
  }

  return (
    <div className="space-y-6 p-5">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">多语言管理</h2>
          <p className="text-muted-foreground">
            管理系统中的多语言字典，支持中文、英文和越南语
          </p>
        </div>
        <div className="flex gap-2">
          <div className="flex items-center gap-2">
            <IconFilter className="h-4 w-4" />
            <Select value={selectedModule} onValueChange={setSelectedModule}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择模块" />
              </SelectTrigger>
              <SelectContent>
                                 <SelectItem value="all">所有模块</SelectItem>
                 {getAllModules().map(module => (
                   <SelectItem key={module} value={module}>
                     <div className="flex flex-col items-start">
                       <span className="font-medium">{module}</span>
                       {moduleDescriptions[module] && (
                         <span className="text-xs text-muted-foreground">{moduleDescriptions[module]}</span>
                       )}
                     </div>
                   </SelectItem>
                 ))}
              </SelectContent>
            </Select>
          </div>
          
          <Dialog open={isAddModuleDialogOpen} onOpenChange={setIsAddModuleDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <IconPlus className="h-4 w-4 mr-2" />
                新建模块
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>创建新模块</DialogTitle>
                <DialogDescription>
                  输入新模块的名称，系统会自动创建示例翻译
                </DialogDescription>
              </DialogHeader>
                             <div className="space-y-4">
                 <div>
                   <Label htmlFor="module-name" className="block mb-1.5">模块名称</Label>
                   <Input
                     id="module-name"
                     value={newModuleName}
                     onChange={(e) => setNewModuleName(e.target.value)}
                     placeholder="例如: common, user, product"
                   />
                 </div>
                 <div>
                   <Label htmlFor="module-desc" className="block mb-1.5">模块描述</Label>
                   <Input
                     id="module-desc"
                     value={newModuleDesc}
                     onChange={(e) => setNewModuleDesc(e.target.value)}
                     placeholder="描述这个模块的用途"
                   />
                 </div>
               </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddModuleDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={addNewModule}>创建模块</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <IconPlus className="h-4 w-4 mr-2" />
                添加翻译
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>添加新的翻译键</DialogTitle>
                <DialogDescription>
                  为新的翻译键添加多语言内容
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="new-key">翻译键</Label>
                  <Input
                    id="new-key"
                    value={newKey}
                    onChange={(e) => setNewKey(e.target.value)}
                    placeholder="例如: common.save"
                  />
                </div>
                {languages.map(lang => (
                  <div key={lang.code}>
                    <Label htmlFor={`new-value-${lang.code}`}>
                      {lang.flag} {lang.name}
                    </Label>
                    <Textarea
                      id={`new-value-${lang.code}`}
                      value={newValues[lang.code] || ''}
                      onChange={(e) => setNewValues(prev => ({
                        ...prev,
                        [lang.code]: e.target.value
                      }))}
                      placeholder={`输入${lang.name}翻译`}
                    />
                  </div>
                ))}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={addNewKey}>添加</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <Button onClick={saveChanges} className="bg-green-600 hover:bg-green-700">
            <IconDeviceFloppy className="h-4 w-4 mr-2" />
            保存所有修改
          </Button>
        </div>
      </div>

      <Tabs defaultValue="table" className="w-full">
        <TabsList>
          <TabsTrigger value="table">表格视图</TabsTrigger>
          <TabsTrigger value="languages">按语言查看</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>翻译字典表格</CardTitle>
              <CardDescription>
                查看和编辑所有语言的翻译内容 
                {selectedModule !== 'all' && (
                  <span className="block mt-1">
                    <strong>{selectedModule}</strong> 模块
                    {moduleDescriptions[selectedModule] && (
                      <span className="text-muted-foreground"> - {moduleDescriptions[selectedModule]}</span>
                    )}
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-48">翻译键</TableHead>
                    {languages.map(lang => (
                      <TableHead key={lang.code}>
                        {lang.flag} {lang.name}
                      </TableHead>
                    ))}
                    <TableHead className="w-32">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getFilteredKeys().map(key => (
                    <TableRow key={key} className={isRowModified(key) ? 'bg-yellow-50' : ''}>
                      <TableCell className="font-medium">
                        <Badge variant="outline" className="text-xs">{key}</Badge>
                      </TableCell>
                      {languages.map(lang => (
                                                 <TableCell key={lang.code} className="max-w-xs">
                           {editingKey === key ? (
                             <div className="space-y-2">
                               {isRowModified(key) && (
                                 <div className="text-xs">
                                   {renderTextDiff(
                                     originalDictionaries[lang.code]?.[key] || '',
                                     dictionaries[lang.code]?.[key] || ''
                                   )}
                                 </div>
                               )}
                               <Textarea
                                 value={dictionaries[lang.code]?.[key] || ''}
                                 onChange={(e) => updateDictionaryValue(lang.code, key, e.target.value)}
                                 className="min-h-16 text-sm"
                                 rows={2}
                               />
                             </div>
                           ) : (
                             <div className="text-sm">
                               {isRowModified(key) ? (
                                 <div className="relative">
                                   <span className="bg-yellow-100 px-1 py-0.5 rounded">
                                     {dictionaries[lang.code]?.[key] || <span className="text-gray-400">未翻译</span>}
                                   </span>
                                   <div className="absolute -top-1 -right-1">
                                     <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                   </div>
                                 </div>
                               ) : (
                                 dictionaries[lang.code]?.[key] || <span className="text-gray-400">未翻译</span>
                               )}
                             </div>
                           )}
                         </TableCell>
                      ))}
                      <TableCell>
                        <div className="flex gap-1">
                          {editingKey === key ? (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => saveRow(key)}
                                className="h-8 w-8 p-0"
                              >
                                <IconCheck className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => cancelRowEdit(key)}
                                className="h-8 w-8 p-0"
                              >
                                <IconX className="h-4 w-4" />
                              </Button>
                            </>
                          ) : (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setEditingKey(key)}
                                className="h-8 w-8 p-0"
                              >
                                <IconEdit className="h-4 w-4" />
                              </Button>
                              {isRowModified(key) && (
                                <Button
                                  size="sm"
                                  onClick={() => saveRow(key)}
                                  className="h-8 w-8 p-0 bg-green-600 hover:bg-green-700"
                                >
                                  <IconDeviceFloppy className="h-4 w-4" />
                                </Button>
                              )}
                            </>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              if (confirm('确定要删除这个翻译键吗？')) {
                                deleteDictionaryKey(key);
                              }
                            }}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <IconTrash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {getFilteredKeys().length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  {selectedModule === 'all' ? '暂无翻译数据' : `${selectedModule} 模块暂无数据`}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="languages" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {languages.map(lang => (
              <Card key={lang.code}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <span className="text-2xl">{lang.flag}</span>
                    {lang.name}
                  </CardTitle>
                  <CardDescription>
                    {Object.keys(dictionaries[lang.code] || {}).length} 个翻译项
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {Object.entries(dictionaries[lang.code] || {}).map(([key, value]) => (
                      <div key={key} className="border-b pb-2">
                        <div className="text-sm font-medium text-gray-600">{key}</div>
                        <div className="text-sm">{value}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 