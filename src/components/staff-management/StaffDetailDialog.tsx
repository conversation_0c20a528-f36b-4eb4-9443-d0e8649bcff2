"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { StaffDetail } from '@/api/staff/types'
import { UserGenderMap } from '@/api/enum'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { 
  User, 
  Phone, 
  Mail, 
  Building, 
  Briefcase, 
  Calendar, 
  CalendarClock,
  MessageSquare,
  UserCheck
} from 'lucide-react'

interface StaffDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  staff?: StaffDetail
  onEdit?: (staff: StaffDetail) => void
}

export function StaffDetailDialog({
  open,
  onOpenChange,
  staff,
  onEdit,
}: StaffDetailDialogProps) {
  if (!staff) return null

  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN })
    } catch {
      return dateString
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            员工档案详情
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <User className="h-4 w-4" />
              基本信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">姓名</label>
                <p className="text-base">{staff.name}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">性别</label>
                <p className="text-base">{UserGenderMap[staff.gender as keyof typeof UserGenderMap] || '未知'}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">员工编号</label>
                <p className="text-base font-mono">{staff.index}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">密码状态</label>
                <Badge variant={staff.is_default_password ? "destructive" : "default"}>
                  {staff.is_default_password ? "默认密码" : "已修改"}
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* 联系信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Phone className="h-4 w-4" />
              联系信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">电话</label>
                <p className="text-base">{staff.phone || '-'}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">邮箱</label>
                <p className="text-base">{staff.email || '-'}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* 职位信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Briefcase className="h-4 w-4" />
              职位信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">部门</label>
                <Badge variant="outline" className="text-base">
                  <Building className="h-3 w-3 mr-1" />
                  {staff.department?.name || '-'}
                </Badge>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">职位</label>
                <Badge variant="secondary" className="text-base">
                  <Briefcase className="h-3 w-3 mr-1" />
                  {staff.position?.name || '-'}
                </Badge>
              </div>
              <div className="space-y-2 md:col-span-2">
                <label className="text-sm font-medium text-muted-foreground">区域</label>
                <Badge variant="default" className="text-base bg-blue-100 text-blue-800 hover:bg-blue-200">
                  <Building className="h-3 w-3 mr-1" />
                  {staff.region?.name || '-'}
                </Badge>
              </div>
            </div>
          </div>

          <Separator />

          {/* 时间信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              时间信息
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">入职日期</label>
                <p className="text-base flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  {formatDate(staff.join_date)}
                </p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">合同到期日期</label>
                <p className="text-base flex items-center gap-2">
                  <CalendarClock className="h-4 w-4 text-muted-foreground" />
                  {formatDate(staff.contract_expiry_date)}
                </p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">最后登录</label>
                <p className="text-base text-muted-foreground">
                  {staff.last_login ? formatDate(staff.last_login) : '从未登录'}
                </p>
              </div>
            </div>
          </div>

          {/* 备注信息 */}
          {staff.remark && (
            <>
              <Separator />
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  备注信息
                </h3>
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-base leading-relaxed">{staff.remark}</p>
                </div>
              </div>
            </>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
            {onEdit && (
              <Button onClick={() => onEdit(staff)}>
                编辑员工
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 