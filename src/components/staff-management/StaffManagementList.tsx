"use client"

import {useCallback, useEffect, useState} from 'react'
import {StaffsTable} from './StaffManagementTable'
import {StaffsSearch} from './StaffManagementSearch'
import {StaffsManagementDialog} from './StaffManagementDialog'
import {StaffDetailDialog} from './StaffDetailDialog'
import {StaffFilter, type StaffFilters} from './StaffFilter'
import {toast} from 'sonner'
import {Loader2} from 'lucide-react'
import {useDebounce} from '@/hooks/use-debounce'
import {StaffDetail, StaffOperation} from '@/api/staff/types'
import {ConfirmDeleteDialog} from './ConfirmDeleteDialog'
import {getMockPaginatedStaff, mockStaffData} from '@/lib/mock-staff-data'


export function StaffManagementList() {
  const [staffs, setStaffs] = useState<StaffDetail[]>([])
  const [isLoading, setIsLoading] = useState(true) // 初始加载
  const [isSaving, setIsSaving] = useState(false) // 保存操作加载
  const [isDeleting, setIsDeleting] = useState(false) // 删除操作加载状态
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<StaffFilters>({})
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize] = useState(16)
  
  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogTitle, setDialogTitle] = useState('')
  const [currentStaff, setCurrentStaff] = useState<StaffDetail | undefined>(undefined)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false) // 删除确认对话框状态
  const [staffToDelete, setStaffToDelete] = useState<StaffDetail | null>(null) // 要删除的客户信息
  
  // 详情查看对话框状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [detailStaff, setDetailStaff] = useState<StaffDetail | undefined>(undefined)
  
  // 获取分页客户列表 - 使用mock数据
  const fetchPaginatedStaffs = useCallback(async (page: number, search: string, filters: StaffFilters) => {
    setIsLoading(true) 
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const mockResponse = getMockPaginatedStaff(page, pageSize, search, filters)
      setStaffs(mockResponse.staffs)
      setTotalPages(mockResponse.total_pages)
    } catch (error) {
      console.error("获取员工列表错误:", error)
      toast.error("获取员工列表失败，请稍后重试")
      setStaffs([])
      setTotalPages(1)
    } finally {
      setIsLoading(false)
    }
  }, [pageSize])
  
  // 初始化加载和页面变化时加载数据
  useEffect(() => {
    fetchPaginatedStaffs(currentPage, debouncedSearchTerm, filters)
  }, [currentPage, debouncedSearchTerm, filters, fetchPaginatedStaffs])
  
  // 处理搜索输入变化
  const handleSearchInputChange = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1) 
  }
  
  // 处理页面变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  // 打开新增客户对话框
  const handleAddClick = () => {
    setCurrentStaff(undefined)
    setDialogTitle('新增员工')
    setDialogOpen(true)
  }
  
  // 打开编辑客户对话框
  const handleEditClick = (staff: StaffDetail) => {
    setCurrentStaff(staff)
    setDialogTitle('编辑员工')
    setDialogOpen(true)
  }
  
  // 打开员工详情查看对话框
  const handleViewClick = (staff: StaffDetail) => {
    setDetailStaff(staff)
    setDetailDialogOpen(true)
  }
  
  // 从详情对话框打开编辑对话框
  const handleEditFromDetail = (staff: StaffDetail) => {
    setDetailDialogOpen(false)
    setCurrentStaff(staff)
    setDialogTitle('编辑员工')
    setDialogOpen(true)
  }
  
  // 修改 handleDeleteClick 以打开确认对话框
  const handleDeleteClick = (staff: StaffDetail) => {
    setStaffToDelete(staff)
    setIsDeleteDialogOpen(true)
  }
  
  // 新增 handleConfirmDelete 函数处理确认删除逻辑 - 使用mock数据
  const handleConfirmDelete = async () => {
    if (!staffToDelete) return;

    setIsDeleting(true)
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 从mock数据中删除
      const index = mockStaffData.findIndex(staff => staff.index === staffToDelete.index)
      if (index !== -1) {
        mockStaffData.splice(index, 1)
        toast.success(`员工 "${staffToDelete.name}" 删除成功`)
        setIsDeleteDialogOpen(false)
        
        // 刷新列表，处理边界情况
        if (staffs.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1)
        } else {
          fetchPaginatedStaffs(currentPage, debouncedSearchTerm, filters)
        }
      } else {
        toast.error("删除员工失败: 员工不存在")
        setIsDeleteDialogOpen(false)
      }
    } catch (error) {
      console.error("删除员工错误:", error)
      toast.error("删除员工时发生错误，请稍后重试")
      setIsDeleteDialogOpen(false)
    } finally {
      setIsDeleting(false)
      setStaffToDelete(null)
    }
  }
  
  // 处理保存员工 - 使用mock数据
  const handleSaveStaff = async (staffData: Partial<StaffDetail> & { index?: string }) => {
    setIsSaving(true)
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (staffData.index) {
        // 编辑现有员工
        const index = mockStaffData.findIndex(staff => staff.index === staffData.index)
        if (index !== -1) {
          mockStaffData[index] = {
            ...mockStaffData[index],
            name: staffData.name || '',
            phone: staffData.phone || '',
            email: staffData.email || '',
            remark: staffData.remark || '',
            gender: staffData.gender || 0,
            department: staffData.department || mockStaffData[index].department,
            position: staffData.position || mockStaffData[index].position,
            region: staffData.region || mockStaffData[index].region,
            join_date: staffData.join_date || '',
            contract_expiry_date: staffData.contract_expiry_date || '',
          }
          
          toast.success(`员工 "${staffData.name}" 更新成功`)
          setDialogOpen(false)
          fetchPaginatedStaffs(currentPage, debouncedSearchTerm, filters)
        } else {
          toast.error("更新员工失败: 员工不存在")
        }
      } else {
        // 新增员工
        const newStaff: StaffDetail = {
          index: `EMP${String(mockStaffData.length + 1).padStart(3, '0')}`,
          name: staffData.name || '',
          phone: staffData.phone || '',
          email: staffData.email || '',
          remark: staffData.remark || '',
          gender: staffData.gender || 0,
          department: staffData.department!,
          position: staffData.position!,
          region: staffData.region,
          join_date: staffData.join_date || '',
          contract_expiry_date: staffData.contract_expiry_date || '',
          last_login: '',
          is_default_password: true,
        }
        
        mockStaffData.push(newStaff)
        toast.success(`员工 "${newStaff.name}" 创建成功`)
        setDialogOpen(false)
        
        // 刷新列表到第一页
        if (currentPage === 1) {
          fetchPaginatedStaffs(1, debouncedSearchTerm, filters)
        } else {
          setCurrentPage(1)
        }
      }
    } catch (error) {
      console.error("保存员工错误:", error)
      toast.error("保存员工时发生错误，请稍后重试")
    } finally {
      setIsSaving(false)
    }
  }
  
  // 处理过滤器变化
  const handleFiltersChange = (newFilters: StaffFilters) => {
    setFilters(newFilters)
    setCurrentPage(1) // 重置到第一页
  }

  // 显示全页面加载状态
  // 注意：只在没有搜索词且首次加载时显示全屏加载
  // 如果是带搜索词的加载或者翻页加载，使用表格内的加载状态
  if (isLoading && staffs.length === 0 && !debouncedSearchTerm) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">正在加载员工数据...</p>
        </div>
      </div>
    )
  }

  return (
    <>
        <StaffsSearch 
          onSearch={handleSearchInputChange} 
          onAddClick={handleAddClick} 
        />
        
        <StaffFilter 
          onFiltersChange={handleFiltersChange}
          className="mb-4"
        />
        
        <StaffsTable 
          staffs={staffs} 
          isLoading={isLoading && staffs.length === 0} 
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onView={handleViewClick}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
        
        <StaffsManagementDialog 
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          staff={currentStaff}
          onSave={handleSaveStaff}
          title={dialogTitle}
          isSaving={isSaving}
        />

        <ConfirmDeleteDialog 
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          staff={staffToDelete}
          onConfirm={handleConfirmDelete}
          isDeleting={isDeleting}
        />

        <StaffDetailDialog
          open={detailDialogOpen}
          onOpenChange={setDetailDialogOpen}
          staff={detailStaff}
          onEdit={handleEditFromDetail}
        />
    </>
  )
} 