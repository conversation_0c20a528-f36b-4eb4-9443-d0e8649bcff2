"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, RotateCcw } from 'lucide-react'
import { Department, Position, Region } from '@/api/staff/types'
import { mockDepartments, mockPositions, mockRegions } from '@/lib/mock-staff-data'
import { UserGenderMap } from '@/api/enum'

export interface StaffFilters {
  department?: Department
  position?: Position
  region?: Region
  gender?: number
}

interface StaffFilterProps {
  onFiltersChange: (filters: StaffFilters) => void
  className?: string
}

export function StaffFilter({ onFiltersChange, className }: StaffFilterProps) {
  const [filters, setFilters] = useState<StaffFilters>({})
  const [showFilters, setShowFilters] = useState(false)

  const departmentList = mockDepartments
  const positionList = mockPositions
  const regionList = mockRegions

  useEffect(() => {
    onFiltersChange(filters)
  }, [filters, onFiltersChange])

  const updateFilter = (key: keyof StaffFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const removeFilter = (key: keyof StaffFilters) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }

  const clearAllFilters = () => {
    setFilters({})
  }

  const hasActiveFilters = Object.keys(filters).length > 0

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 过滤器切换按钮 */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          筛选条件
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-1">
              {Object.keys(filters).length}
            </Badge>
          )}
        </Button>
        
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <RotateCcw className="h-4 w-4" />
            清除筛选
          </Button>
        )}
      </div>

      {/* 筛选器面板 */}
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
          {/* 部门筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">部门</label>
            <Select
              value={filters.department?.id.toString() || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  const dept = departmentList.find(d => d.id.toString() === value)
                  updateFilter('department', dept)
                } else {
                  removeFilter('department')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择部门" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部部门</SelectItem>
                {departmentList.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id.toString()}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 职位筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">职位</label>
            <Select
              value={filters.position?.id.toString() || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  const pos = positionList.find(p => p.id.toString() === value)
                  updateFilter('position', pos)
                } else {
                  removeFilter('position')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择职位" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部职位</SelectItem>
                {positionList.map((pos) => (
                  <SelectItem key={pos.id} value={pos.id.toString()}>
                    {pos.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 区域筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">区域</label>
            <Select
              value={filters.region?.id.toString() || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  const region = regionList.find(r => r.id.toString() === value)
                  updateFilter('region', region)
                } else {
                  removeFilter('region')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择区域" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部区域</SelectItem>
                {regionList.map((region) => (
                  <SelectItem key={region.id} value={region.id.toString()}>
                    {region.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 性别筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">性别</label>
            <Select
              value={filters.gender?.toString() || "all"}
              onValueChange={(value) => {
                if (value && value !== "all") {
                  updateFilter('gender', parseInt(value))
                } else {
                  removeFilter('gender')
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择性别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部性别</SelectItem>
                <SelectItem value="1">男</SelectItem>
                <SelectItem value="2">女</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* 激活的筛选器标签 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filters.department && (
            <Badge variant="secondary" className="flex items-center gap-1">
              部门: {filters.department.name}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => removeFilter('department')}
              />
            </Badge>
          )}
          {filters.position && (
            <Badge variant="secondary" className="flex items-center gap-1">
              职位: {filters.position.name}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => removeFilter('position')}
              />
            </Badge>
          )}
          {filters.region && (
            <Badge variant="secondary" className="flex items-center gap-1">
              区域: {filters.region.name}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => removeFilter('region')}
              />
            </Badge>
          )}
          {filters.gender && (
            <Badge variant="secondary" className="flex items-center gap-1">
              性别: {UserGenderMap[filters.gender as keyof typeof UserGenderMap]}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                onClick={() => removeFilter('gender')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
} 