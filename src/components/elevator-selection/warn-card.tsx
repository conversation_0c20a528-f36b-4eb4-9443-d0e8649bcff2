// 在 selection-container.tsx 文件顶部添加 WarnCard 组件
import { AlertTriangle, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface WarnCardProps {
  warnings: string[];
  onDismiss?: () => void;
  showDismiss?: boolean;
}

export function WarnCard({ warnings, onDismiss, showDismiss = true }: WarnCardProps) {
  if (!warnings || warnings.length === 0) {
    return null;
  }

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-lg shadow-sm overflow-hidden">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="font-medium text-amber-800 mb-2">注意事项</h3>
              <div className="space-y-1">
                {warnings.map((warning, index) => (
                  <p key={index} className="text-amber-700 text-sm">
                    • {warning}
                  </p>
                ))}
              </div>
            </div>
          </div>
          {showDismiss && onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="text-amber-600 hover:text-amber-800 hover:bg-amber-100 p-1 h-auto"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
