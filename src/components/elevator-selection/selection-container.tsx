import React, { useState, useEffect } from "react";
import { SelectionForm } from "./selection-form";
import { SelectionResult } from "./selection-result";
import { CalculatorLog } from "./calculator-log";
import {
  ElevatorSelectionData,
  CalculatedResult,
  getElevatorSelection,
  saveElevatorSelection,
  getPresetDefaultData,
  Shaft,
} from "@/lib/elevator-selection";
import { OpportunityData } from "@/components/dashboard/opportunity-card";
import { calculateElevatorRecommend } from "@/api/calculate/api";

interface SelectionContainerProps {
  opportunityData: OpportunityData;
  onSave: () => void;
}

export function SelectionContainer({ opportunityData, onSave }: SelectionContainerProps) {
  // 页面初始化状态
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  // 获取已保存的选型数据或使用预设默认数据
  const [selectionData, setSelectionData] = useState<ElevatorSelectionData>(
    getElevatorSelection(opportunityData.id) || getPresetDefaultData(opportunityData.id)
  );

  const [recommendWarnings, setRecommendWarnings] = useState<string[]>([]);
  const [recommendShaftData, setRecommendShaftData] = useState<Shaft | null>(null);

  // 页面初始化逻辑
  useEffect(() => {
    const initializePage = async () => {
      try {
        setIsInitializing(true);
        setInitError(null);

        // 检查是否已有保存的数据
        const savedData = getElevatorSelection(opportunityData.id);
        if (savedData) {
          // 如果有保存的数据，直接使用
          setSelectionData(savedData);
          setIsInitializing(false);
          return;
        }

        // 如果没有保存的数据，使用预设默认值并获取推荐参数
        const defaultData = getPresetDefaultData(opportunityData.id);
        setSelectionData(defaultData);

        // 调用推荐接口获取推荐井道参数
        const baseData = {
          Lift_Model: defaultData.liftModel,
          Capacity: defaultData.capacity,
          Speed: defaultData.speed,
          Travel_Height: defaultData.travelHeight,
          Car_Width: defaultData.carWidth,
          Car_Depth: defaultData.carDepth,
          Car_Height: defaultData.carHeight,
          CWT_Position: defaultData.cwtPosition,
          CWT_Safety_Gear: defaultData.cwtSafetyGear ? "Yes" : "No",
          Door_Opening: defaultData.doorOpening,
          Door_Width: defaultData.doorWidth,
          Door_Height: defaultData.doorHeight,
          Through_Door: defaultData.throughDoor ? "Yes" : "No",
          Glass_Door: defaultData.glassDoor ? "Yes" : "No",
          Standard: defaultData.standard,
          Door_Center_from_Car_Center: defaultData.doorCenterPosition,
          Car_Area_Exceed_the_Code: defaultData.floorExceedCode ? "Yes" : "No Allow",
          Shaft_Tolerance: defaultData.shaftTolerance,
          Marble_Floor: defaultData.marbleFloorThickness,
          Shaft_Width: defaultData.shaftWidth,
          Shaft_Depth: defaultData.shaftDepth,
          Overhead: defaultData.overhead,
          Pit_Depth: defaultData.pitDepth,
        };

        console.log("baseData", baseData);

        const { data: recommend } = await calculateElevatorRecommend(baseData);

        // 更新推荐井道数据
        setRecommendShaftData({
          shaftWidth: recommend.Shaft_Width_min,
          shaftDepth: recommend.Shaft_Depth_min,
          overheadHeight: recommend.Shaft_Height_min,
          pitDepth: recommend.Shaft_Pit_min,
        });

        // 处理警告信息
        if (recommend.warn && recommend.warning_text?.length > 0) {
          setRecommendWarnings(recommend.warning_text);
        }

      } catch (error) {
        console.error("页面初始化失败:", error);
        setInitError("页面初始化失败，请刷新重试");
      } finally {
        setIsInitializing(false);
      }
    };

    initializePage();
  }, [opportunityData.id]);

  // 计算结果
  const calculatedResult: CalculatedResult = {
    capacity: selectionData.capacity,
    persons: Math.floor(selectionData.capacity / 75),
    shaftWidth: selectionData.shaftWidth,
    shaftDepth: selectionData.shaftDepth,
    overheadHeight: selectionData.overhead,
    pitDepth: selectionData.pitDepth,
  };

  // 更新选型数据
  const handleChange = (partialData: Partial<ElevatorSelectionData>) => {
    setSelectionData(prev => ({
      ...prev,
      ...partialData,
      lastUpdated: new Date().toISOString(),
    }));
  };

  // 保存选型数据
  const handleSave = () => {
    // 设置提交标志和时间
    const dataToSave = {
      ...selectionData,
      isSubmitted: true,
      submittedAt: new Date().toISOString(),
    };
    saveElevatorSelection(dataToSave);
    setSelectionData(dataToSave); // 更新本地状态
    onSave();
  };

  // 如果正在初始化，显示loading状态
  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-6">
          {/* 苹果风格的loading动画 */}
          <div className="relative">
            <div className="w-16 h-16 mx-auto">
              <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
              <div className="absolute inset-0 rounded-full border-4 border-[#00B4AA] border-t-transparent animate-spin"></div>
            </div>
          </div>

          {/* Loading文本 */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-gray-800">正在初始化选型参数</h2>
            <p className="text-sm text-gray-600">正在获取推荐井道参数，请稍候...</p>
          </div>

          {/* 进度指示器 */}
          <div className="w-64 mx-auto">
            <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-[#00B4AA] to-[#0092D8] rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 如果初始化出错，显示错误状态
  if (initError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-gray-800">初始化失败</h2>
            <p className="text-sm text-gray-600">{initError}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-[#00B4AA] text-white rounded-lg hover:bg-[#009B92] transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 max-w-6xl mx-auto p-4 relative">
      {/* 顶部标题 - 公司标志和标题 */}
      <div className="flex items-center justify-center mb-4">
        <div className="text-center">
          <h1 className="text-xl font-bold mb-1">电梯土建尺寸规划</h1>
          <p className="text-md">Reassuring Planning Tool (RPT)</p>
        </div>
        <div className="w-32"></div> {/* 右侧占位 */}
      </div>

      {/* 项目信息展示 */}
      <div className="flex flex-col space-y-2 mb-4">
        <div className="flex items-center">
          <div className="w-28 font-semibold">项目名称:</div>
          <div>{opportunityData?.projectName || "N/A"}</div>
        </div>
        <div className="flex items-center">
          <div className="w-28 font-semibold">项目编号:</div>
          <div>{opportunityData?.projectCode || "N/A"}</div>
        </div>
        <div className="flex items-center">
          <div className="w-28 font-semibold">客户名称:</div>
          <div>{opportunityData?.customerName || "N/A"}</div>
        </div>
      </div>

      {/* 选型表单 */}
      <SelectionForm
        data={selectionData}
        setRecommendWarnings={setRecommendWarnings}
        recommendShaftData={recommendShaftData}
        setRecommendShaftData={setRecommendShaftData}
        onChange={handleChange}
      />

      {/* 选型结果 */}
      <SelectionResult
        result={calculatedResult}
        recommendShaftData={recommendShaftData}
        recommendWarnings={recommendWarnings}
      />

      {/* 计算器日志 - 浮动在右下角 */}
      <CalculatorLog data={selectionData} />
    </div>
  );
}
