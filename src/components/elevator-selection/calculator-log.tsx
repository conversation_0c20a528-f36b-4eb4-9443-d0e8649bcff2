import React, { useState, useMemo } from "react";
import { ElevatorSelectionData } from "@/lib/elevator-selection";
import { calculateWarnings, Warning, WarningResult } from "./calculator-warnings";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronDown, 
  ChevronUp, 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  Calculator,
  Settings
} from "lucide-react";
import { cn } from "@/lib/utils";

interface CalculatorLogProps {
  data: ElevatorSelectionData;
  className?: string;
}

export function CalculatorLog({ data, className }: CalculatorLogProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [selectedWarning, setSelectedWarning] = useState<Warning | null>(null);

  // 计算警告
  const warningResult: WarningResult = useMemo(() => {
    return calculateWarnings(data);
  }, [data]);

  const { warnings, errorCount, warningCount, infoCount } = warningResult;
  const totalCount = errorCount + warningCount + infoCount;

  // 获取警告图标
  const getWarningIcon = (type: Warning['type']) => {
    switch (type) {
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  // 获取警告颜色类
  const getWarningColorClass = (type: Warning['type']) => {
    switch (type) {
      case 'error':
        return 'border-l-red-500 bg-red-50';
      case 'warning':
        return 'border-l-yellow-500 bg-yellow-50';
      case 'info':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  // 格式化参数值
  const formatParameterValue = (value: any): string => {
    if (typeof value === 'boolean') {
      return value ? '是' : '否';
    }
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  // 格式化参数键名
  const formatParameterKey = (key: string): string => {
    const keyMap: { [key: string]: string } = {
      'liftModel': '电梯型号',
      'capacity': '载重(kg)',
      'cwtPosition': '对重位置',
      'standard': '标准',
      'throughDoor': '贯通门',
      'shaftWidth': '井道宽度(mm)',
      'minShaftWidth': '最小井道宽度(mm)',
      'carWidth': '轿厢宽度(mm)',
      'doorWidth': '门宽(mm)',
      'shaftDepth': '井道深度(mm)',
      'minShaftDepth': '最小井道深度(mm)',
      'carDepth': '轿厢深度(mm)',
      'overhead': '顶层高度(mm)',
      'minOverhead': '最小顶层高度(mm)',
      'carHeight': '轿厢高度(mm)',
      'speed': '速度(m/s)',
      'pitDepth': '底坑深度(mm)',
      'minPitDepth': '最小底坑深度(mm)',
      'threshold': '阈值',
      'carArea': '轿厢面积(m²)',
      'areaCapacity': '面积对应载重(kg)',
      'selectedCapacity': '选择载重(kg)',
      'calculatedPersons': '计算人数',
      'standardPersons': '标准人数',
      'minCapacity': '最小载重(kg)',
      'maxCapacity': '最大载重(kg)',
      'floorExceedCode': '允许超标',
      'minWidth': '最小宽度(mm)',
      'minDepth': '最小深度(mm)',
      'doorHeight': '门高(mm)',
      'minDoorHeight': '最小门高(mm)',
      'heightDiff': '高度差(mm)',
      'travelHeight': '运行高度(m)',
      'standardTravelHeight': '标准运行高度(m)',
      'doorOverhang': '门宽超出量(mm)',
      'doorOpening': '门开启方式',
      'allowedRange': '允许范围',
      'cwtSafetyGear': '对重安全钳',
      'glassDoor': '玻璃门',
      'doorCenterPosition': '门中心位置',
      'shaftTolerance': '井道公差',
      'marbleFloorThickness': '大理石地板厚度(mm)',
    };
    return keyMap[key] || key;
  };

  // 渲染参数表格
  const renderParametersTable = (parameters?: { [key: string]: any }) => {
    if (!parameters || Object.keys(parameters).length === 0) return null;

    return (
      <div className="mt-3 pt-3 border-t border-gray-200">
        <div className="text-xs font-medium text-gray-700 mb-2">相关参数</div>
        <div className="bg-gray-50 rounded-md overflow-hidden">
          <table className="w-full text-xs">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-2 py-1.5 text-left font-medium text-gray-600 w-1/2">参数</th>
                <th className="px-2 py-1.5 text-left font-medium text-gray-600 w-1/2">值</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(parameters).map(([key, value], index) => (
                <tr key={key} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-2 py-1.5 text-gray-600 border-r border-gray-200">
                    {formatParameterKey(key)}
                  </td>
                  <td className="px-2 py-1.5 font-mono text-gray-800 font-medium">
                    {formatParameterValue(value)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className={cn("fixed bottom-4 right-4 z-50", className)}>
      <Card className="w-80 shadow-lg border-0 bg-white/95 backdrop-blur-sm">
        <CardHeader 
          className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calculator className="w-4 h-4 text-[#00B4AA]" />
              <span>计算器日志</span>
            </div>
            <div className="flex items-center gap-2">
              {/* 警告数量徽章 */}
              <div className="flex items-center gap-1">
                {errorCount > 0 && (
                  <Badge variant="destructive" className="h-5 text-xs">
                    {errorCount}
                  </Badge>
                )}
                {warningCount > 0 && (
                  <Badge variant="outline" className="h-5 text-xs border-yellow-500 text-yellow-600">
                    {warningCount}
                  </Badge>
                )}
                {infoCount > 0 && (
                  <Badge variant="outline" className="h-5 text-xs border-blue-500 text-blue-600">
                    {infoCount}
                  </Badge>
                )}
                {totalCount === 0 && (
                  <Badge variant="outline" className="h-5 text-xs border-green-500 text-green-600">
                    正常
                  </Badge>
                )}
              </div>
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </div>
          </CardTitle>
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0 max-h-96 overflow-y-auto">
            {warnings.length === 0 ? (
              <div className="text-center py-4 text-sm text-gray-500">
                <Settings className="w-8 h-8 mx-auto mb-2 text-green-500" />
                <p>当前配置无警告</p>
                <p className="text-xs">所有参数都在合理范围内</p>
              </div>
            ) : (
              <div className="space-y-3">
                {warnings.map((warning) => (
                  <div
                    key={warning.id}
                    className={cn(
                      "border-l-4 p-3 rounded-r cursor-pointer transition-all hover:shadow-sm",
                      getWarningColorClass(warning.type),
                      selectedWarning?.id === warning.id && "ring-2 ring-[#00B4AA] ring-opacity-50"
                    )}
                    onClick={() => setSelectedWarning(selectedWarning?.id === warning.id ? null : warning)}
                  >
                    <div className="flex items-start gap-2">
                      {getWarningIcon(warning.type)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {warning.title}
                          </h4>
                          <div className="flex items-center gap-1 ml-2">
                            <Badge variant="outline" className="text-xs">
                              {warning.section}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{warning.message}</p>
                        
                        {/* 展开的详细信息 */}
                        {selectedWarning?.id === warning.id && (
                          renderParametersTable(warning.parameters)
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 底部说明 */}
            <div className="mt-4 pt-3 border-t border-gray-200 text-xs text-gray-500 text-center">
              <p>点击警告项查看详细参数</p>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
} 