"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Trash2 } from 'lucide-react'
import { CustomerType, ImportanceLevel, ContactInfo, FullCustomerInfo } from '@/api/customer/types'

interface CustomerFormProps {
  customer?: FullCustomerInfo;
  onSubmit: (customerData: any) => void;
  isLoading?: boolean;
}

interface FormData {
  customer_type: CustomerType;
  // 业主字段
  owner_name: string;
  owner_phone: string;
  owner_contacts: ContactInfo[];
  owner_address: string;
  owner_email: string;
  // 总包字段
  company_name: string;
  company_phone: string;
  maincontractor_contacts: ContactInfo[];
  company_address: string;
  company_email: string;
  // 中间人字段
  broker_name: string;
  broker_phone: string;
  broker_email: string;
  // 共同字段
  importance_level: ImportanceLevel;
  remark: string;
}

export function CustomerForm({ customer, onSubmit, isLoading = false }: CustomerFormProps) {
  const [formData, setFormData] = useState<FormData>({
    customer_type: CustomerType.OWNER,
    // 业主字段
    owner_name: '',
    owner_phone: '',
    owner_contacts: [{ name: '', phone: '' }],
    owner_address: '',
    owner_email: '',
    // 总包字段
    company_name: '',
    company_phone: '',
    maincontractor_contacts: [{ name: '', phone: '' }],
    company_address: '',
    company_email: '',
    // 中间人字段
    broker_name: '',
    broker_phone: '',
    broker_email: '',
    // 共同字段
    importance_level: ImportanceLevel.B,
    remark: '',
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // 初始化表单数据
  useEffect(() => {
    if (customer) {
      setFormData({
        customer_type: customer.customer_type,
        // 业主字段
        owner_name: customer.owner_info?.owner_name || '',
        owner_phone: customer.owner_info?.owner_phone || '',
        owner_contacts: customer.owner_info?.contacts || [{ name: '', phone: '' }],
        owner_address: customer.owner_info?.address || '',
        owner_email: customer.owner_info?.email || '',
        // 总包字段
        company_name: customer.maincontractor_info?.company_name || '',
        company_phone: customer.maincontractor_info?.company_phone || '',
        maincontractor_contacts: customer.maincontractor_info?.contacts || [{ name: '', phone: '' }],
        company_address: customer.maincontractor_info?.company_address || '',
        company_email: customer.maincontractor_info?.company_email || '',
        // 中间人字段
        broker_name: customer.broker_info?.broker_name || '',
        broker_phone: customer.broker_info?.broker_phone || '',
        broker_email: customer.broker_info?.broker_email || '',
        // 共同字段
        importance_level: customer.owner_info?.importance_level || 
                         customer.maincontractor_info?.importance_level || 
                         customer.broker_info?.importance_level || 
                         ImportanceLevel.B,
        remark: customer.owner_info?.remark || 
               customer.maincontractor_info?.remark || 
               customer.broker_info?.remark || 
               '',
      })
    }
  }, [customer])

  // 更新联系人
  const updateContact = (index: number, field: 'name' | 'phone', value: string, type: 'owner' | 'maincontractor') => {
    const contactsField = type === 'owner' ? 'owner_contacts' : 'maincontractor_contacts'
    const newContacts = [...formData[contactsField]]
    newContacts[index][field] = value
    setFormData({ ...formData, [contactsField]: newContacts })
  }

  // 添加联系人
  const addContact = (type: 'owner' | 'maincontractor') => {
    const contactsField = type === 'owner' ? 'owner_contacts' : 'maincontractor_contacts'
    setFormData({
      ...formData,
      [contactsField]: [...formData[contactsField], { name: '', phone: '' }]
    })
  }

  // 删除联系人
  const removeContact = (index: number, type: 'owner' | 'maincontractor') => {
    const contactsField = type === 'owner' ? 'owner_contacts' : 'maincontractor_contacts'
    const newContacts = formData[contactsField].filter((_, i) => i !== index)
    if (newContacts.length === 0) {
      newContacts.push({ name: '', phone: '' })
    }
    setFormData({ ...formData, [contactsField]: newContacts })
  }

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // 根据客户类型验证
    switch (formData.customer_type) {
      case CustomerType.OWNER:
        if (!formData.owner_name.trim()) newErrors.owner_name = '业主名称不能为空'
        if (!formData.owner_phone.trim()) newErrors.owner_phone = '业主电话不能为空'
        if (!formData.owner_address.trim()) newErrors.owner_address = '业主地址不能为空'
        
        // 验证联系人（至少一个有效联系人）
        const validOwnerContacts = formData.owner_contacts.filter(c => c.name.trim() && c.phone.trim())
        if (validOwnerContacts.length === 0) {
          newErrors.owner_contacts = '至少需要一个有效的联系人'
        }
        break

      case CustomerType.MAINCONTRACTOR:
        if (!formData.company_name.trim()) newErrors.company_name = '公司名称不能为空'
        if (!formData.company_phone.trim()) newErrors.company_phone = '公司电话不能为空'
        if (!formData.company_address.trim()) newErrors.company_address = '公司地址不能为空'
        
        // 验证联系人（至少一个有效联系人）
        const validMainContacts = formData.maincontractor_contacts.filter(c => c.name.trim() && c.phone.trim())
        if (validMainContacts.length === 0) {
          newErrors.maincontractor_contacts = '至少需要一个有效的联系人'
        }
        break

      case CustomerType.BROKER:
        if (!formData.broker_name.trim()) newErrors.broker_name = '中间人姓名不能为空'
        if (!formData.broker_phone.trim()) newErrors.broker_phone = '中间人电话不能为空'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = () => {
    if (!validateForm() || isLoading) return

    const currentDate = new Date().toISOString().split('T')[0]
    const staffMember = "当前登录用户" // 这里应该从用户上下文获取

    const submitData: any = {
      customer_type: formData.customer_type,
    }

    // 根据客户类型组装数据
    switch (formData.customer_type) {
      case CustomerType.OWNER:
        submitData.owner_info = {
          owner_name: formData.owner_name.trim(),
          owner_phone: formData.owner_phone.trim(),
          contacts: formData.owner_contacts.filter(c => c.name.trim() && c.phone.trim()),
          address: formData.owner_address.trim(),
          email: formData.owner_email.trim(),
          registration_date: currentDate,
          staff_member: staffMember,
          importance_level: formData.importance_level,
          remark: formData.remark.trim(),
        }
        break

      case CustomerType.MAINCONTRACTOR:
        submitData.maincontractor_info = {
          company_name: formData.company_name.trim(),
          company_phone: formData.company_phone.trim(),
          contacts: formData.maincontractor_contacts.filter(c => c.name.trim() && c.phone.trim()),
          company_address: formData.company_address.trim(),
          company_email: formData.company_email.trim(),
          registration_date: currentDate,
          staff_member: staffMember,
          importance_level: formData.importance_level,
          remark: formData.remark.trim(),
        }
        break

      case CustomerType.BROKER:
        submitData.broker_info = {
          broker_name: formData.broker_name.trim(),
          broker_phone: formData.broker_phone.trim(),
          broker_email: formData.broker_email.trim(),
          registration_date: currentDate,
          staff_member: staffMember,
          importance_level: formData.importance_level,
          remark: formData.remark.trim(),
        }
        break
    }

    onSubmit(submitData)
  }

  // 渲染联系人字段
  const renderContacts = (contacts: ContactInfo[], type: 'owner' | 'maincontractor') => {
    const errorKey = type === 'owner' ? 'owner_contacts' : 'maincontractor_contacts'
    
    return (
      <div className="space-y-4">
        <Label>联系人信息 <span className="text-destructive">*</span></Label>
        <div className="space-y-3">
          {contacts.map((contact, index) => (
            <div key={index} className="border rounded-lg p-4 bg-gray-50/50">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">联系人 {index + 1}</span>
                {contacts.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContact(index, type)}
                    className="h-7 w-7 p-0 text-gray-500 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-gray-600">姓名</Label>
                  <Input
                    value={contact.name}
                    onChange={(e) => updateContact(index, 'name', e.target.value, type)}
                    placeholder="联系人姓名"
                    className="h-9"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-gray-600">电话</Label>
                  <Input
                    value={contact.phone}
                    onChange={(e) => updateContact(index, 'phone', e.target.value, type)}
                    placeholder="联系人电话"
                    className="h-9"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={() => addContact(type)}
          className="w-full h-9 text-sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          添加联系人
        </Button>
        {errors[errorKey] && (
          <p className="text-sm text-destructive">{errors[errorKey]}</p>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 客户类型选择 */}
      <div className="space-y-2">
        <Label>客户类型 <span className="text-destructive">*</span></Label>
        <Select 
          value={formData.customer_type} 
          onValueChange={(value: CustomerType) => setFormData({ ...formData, customer_type: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="选择客户类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={CustomerType.OWNER}>业主</SelectItem>
            <SelectItem value={CustomerType.MAINCONTRACTOR}>总包</SelectItem>
            <SelectItem value={CustomerType.BROKER}>中间人</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 根据客户类型显示不同字段 */}
      {formData.customer_type === CustomerType.OWNER && (
        <Card>
          <CardHeader>
            <CardTitle>业主信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>业主名称 <span className="text-destructive">*</span></Label>
                <Input
                  value={formData.owner_name}
                  onChange={(e) => setFormData({ ...formData, owner_name: e.target.value })}
                  placeholder="请输入业主名称"
                />
                {errors.owner_name && <p className="text-sm text-destructive">{errors.owner_name}</p>}
              </div>
              <div className="space-y-2">
                <Label>业主电话 <span className="text-destructive">*</span></Label>
                <Input
                  value={formData.owner_phone}
                  onChange={(e) => setFormData({ ...formData, owner_phone: e.target.value })}
                  placeholder="请输入业主电话"
                />
                {errors.owner_phone && <p className="text-sm text-destructive">{errors.owner_phone}</p>}
              </div>
            </div>

            {renderContacts(formData.owner_contacts, 'owner')}

            <div className="space-y-2">
              <Label>业主地址 <span className="text-destructive">*</span></Label>
              <Textarea
                value={formData.owner_address}
                onChange={(e) => setFormData({ ...formData, owner_address: e.target.value })}
                placeholder="请输入业主详细地址"
                className="resize-none"
                rows={3}
              />
              {errors.owner_address && <p className="text-sm text-destructive">{errors.owner_address}</p>}
            </div>

            <div className="space-y-2">
              <Label>业主邮箱</Label>
              <Input
                type="email"
                value={formData.owner_email}
                onChange={(e) => setFormData({ ...formData, owner_email: e.target.value })}
                placeholder="请输入业主邮箱"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {formData.customer_type === CustomerType.MAINCONTRACTOR && (
        <Card>
          <CardHeader>
            <CardTitle>总包信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>公司名称 <span className="text-destructive">*</span></Label>
                <Input
                  value={formData.company_name}
                  onChange={(e) => setFormData({ ...formData, company_name: e.target.value })}
                  placeholder="请输入总包公司名称"
                />
                {errors.company_name && <p className="text-sm text-destructive">{errors.company_name}</p>}
              </div>
              <div className="space-y-2">
                <Label>公司电话 <span className="text-destructive">*</span></Label>
                <Input
                  value={formData.company_phone}
                  onChange={(e) => setFormData({ ...formData, company_phone: e.target.value })}
                  placeholder="请输入公司电话"
                />
                {errors.company_phone && <p className="text-sm text-destructive">{errors.company_phone}</p>}
              </div>
            </div>

            {renderContacts(formData.maincontractor_contacts, 'maincontractor')}

            <div className="space-y-2">
              <Label>公司地址 <span className="text-destructive">*</span></Label>
              <Textarea
                value={formData.company_address}
                onChange={(e) => setFormData({ ...formData, company_address: e.target.value })}
                placeholder="请输入总包公司地址"
                className="resize-none"
                rows={3}
              />
              {errors.company_address && <p className="text-sm text-destructive">{errors.company_address}</p>}
            </div>

            <div className="space-y-2">
              <Label>公司邮箱</Label>
              <Input
                type="email"
                value={formData.company_email}
                onChange={(e) => setFormData({ ...formData, company_email: e.target.value })}
                placeholder="请输入公司邮箱"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {formData.customer_type === CustomerType.BROKER && (
        <Card>
          <CardHeader>
            <CardTitle>中间人信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>中间人姓名 <span className="text-destructive">*</span></Label>
                <Input
                  value={formData.broker_name}
                  onChange={(e) => setFormData({ ...formData, broker_name: e.target.value })}
                  placeholder="请输入中间人姓名"
                />
                {errors.broker_name && <p className="text-sm text-destructive">{errors.broker_name}</p>}
              </div>
              <div className="space-y-2">
                <Label>中间人电话 <span className="text-destructive">*</span></Label>
                <Input
                  value={formData.broker_phone}
                  onChange={(e) => setFormData({ ...formData, broker_phone: e.target.value })}
                  placeholder="请输入中间人电话"
                />
                {errors.broker_phone && <p className="text-sm text-destructive">{errors.broker_phone}</p>}
              </div>
            </div>

            <div className="space-y-2">
              <Label>中间人邮箱</Label>
              <Input
                type="email"
                value={formData.broker_email}
                onChange={(e) => setFormData({ ...formData, broker_email: e.target.value })}
                placeholder="请输入中间人邮箱"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 共同字段 */}
      <Card>
        <CardHeader>
          <CardTitle>其他信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>重要程度</Label>
            <Select 
              value={formData.importance_level} 
              onValueChange={(value: ImportanceLevel) => setFormData({ ...formData, importance_level: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择重要程度" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={ImportanceLevel.A}>A级</SelectItem>
                <SelectItem value={ImportanceLevel.B}>B级</SelectItem>
                <SelectItem value={ImportanceLevel.C}>C级</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>备注信息</Label>
            <Textarea
              value={formData.remark}
              onChange={(e) => setFormData({ ...formData, remark: e.target.value })}
              placeholder="请输入备注信息"
              className="resize-none"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* 提交按钮 */}
      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={isLoading}
          className="px-8"
        >
          {isLoading ? '保存中...' : '保存'}
        </Button>
      </div>
    </div>
  )
} 