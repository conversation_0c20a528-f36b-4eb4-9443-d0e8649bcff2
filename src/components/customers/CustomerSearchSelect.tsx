"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Search, Check, Users, Building, User } from 'lucide-react'
import { FullCustomerInfo, CustomerType, ImportanceLevel } from '@/api/customer/types'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

// Mock数据
const mockCustomers: FullCustomerInfo[] = [
  {
    id: 1,
    name: '华为技术有限公司',
    customer_type: CustomerType.OWNER,
    index: 'OWN001',
    owner_info: {
      owner_name: '华为技术有限公司',
      owner_phone: '0755-28560000',
      contacts: [
        { name: '张总', phone: '13800138001' },
        { name: '李经理', phone: '13800138002' }
      ],
      address: '广东省深圳市龙岗区坂田华为基地',
      email: '<EMAIL>',
      registration_date: '2024-01-15',
      staff_member: '王业务员',
      importance_level: ImportanceLevel.A,
      remark: '重要客户，项目金额较大'
    }
  },
  {
    id: 2,
    name: '中建三局集团有限公司',
    customer_type: CustomerType.MAINCONTRACTOR,
    index: 'CON001',
    maincontractor_info: {
      company_name: '中建三局集团有限公司',
      company_phone: '027-82926688',
      contacts: [
        { name: '刘项目经理', phone: '13700137001' },
        { name: '陈工程师', phone: '13700137002' }
      ],
      company_address: '湖北省武汉市洪山区中建三局大厦',
      company_email: '<EMAIL>',
      registration_date: '2024-01-20',
      staff_member: '张业务员',
      importance_level: ImportanceLevel.A,
      remark: '长期合作伙伴，信誉良好'
    }
  },
  {
    id: 3,
    name: '腾讯科技',
    customer_type: CustomerType.OWNER,
    index: 'OWN002',
    owner_info: {
      owner_name: '腾讯科技(深圳)有限公司',
      owner_phone: '0755-86013388',
      contacts: [
        { name: '马总监', phone: '13600136001' }
      ],
      address: '广东省深圳市南山区腾讯大厦',
      email: '<EMAIL>',
      registration_date: '2024-02-01',
      staff_member: '李业务员',
      importance_level: ImportanceLevel.B,
      remark: '互联网头部企业'
    }
  },
  {
    id: 4,
    name: '王中介',
    customer_type: CustomerType.BROKER,
    index: 'BRK001',
    broker_info: {
      broker_name: '王中介',
      broker_phone: '13500135001',
      broker_email: '<EMAIL>',
      registration_date: '2024-02-10',
      staff_member: '赵业务员',
      importance_level: ImportanceLevel.B,
      remark: '资源丰富，配合度高'
    }
  },
  {
    id: 5,
    name: '中国建筑股份有限公司',
    customer_type: CustomerType.MAINCONTRACTOR,
    index: 'CON002',
    maincontractor_info: {
      company_name: '中国建筑股份有限公司',
      company_phone: '010-88082888',
      contacts: [
        { name: '周总工', phone: '13400134001' },
        { name: '吴经理', phone: '13400134002' },
        { name: '钱主管', phone: '13400134003' }
      ],
      company_address: '北京市朝阳区金台西路2号院1号楼',
      company_email: '<EMAIL>',
      registration_date: '2024-02-15',
      staff_member: '孙业务员',
      importance_level: ImportanceLevel.A,
      remark: '国内建筑行业龙头企业'
    }
  },
  {
    id: 6,
    name: '阿里巴巴集团',
    customer_type: CustomerType.OWNER,
    index: 'OWN003',
    owner_info: {
      owner_name: '阿里巴巴(中国)有限公司',
      owner_phone: '0571-85022088',
      contacts: [
        { name: '杨总', phone: '13300133001' }
      ],
      address: '浙江省杭州市余杭区文一西路969号',
      email: '<EMAIL>',
      registration_date: '2024-02-20',
      staff_member: '周业务员',
      importance_level: ImportanceLevel.A,
      remark: '电商巨头，多个项目在谈'
    }
  },
  {
    id: 7,
    name: '李经纪人',
    customer_type: CustomerType.BROKER,
    index: 'BRK002',
    broker_info: {
      broker_name: '李经纪人',
      broker_phone: '13200132001',
      broker_email: '<EMAIL>',
      registration_date: '2024-02-25',
      staff_member: '吴业务员',
      importance_level: ImportanceLevel.C,
      remark: '新接触的中介，待观察'
    }
  },
  {
    id: 8,
    name: '万科企业股份有限公司',
    customer_type: CustomerType.OWNER,
    index: 'OWN004',
    owner_info: {
      owner_name: '万科企业股份有限公司',
      owner_phone: '0755-25606666',
      contacts: [
        { name: '赵总', phone: '13100131001' },
        { name: '钱经理', phone: '13100131002' }
      ],
      address: '广东省深圳市盐田区大梅沙环梅路33号',
      email: '<EMAIL>',
      registration_date: '2024-03-01',
      staff_member: '郑业务员',
      importance_level: ImportanceLevel.A,
      remark: '知名房地产开发商'
    }
  }
]

interface CustomerSearchSelectProps {
  onSelectCustomer: (customer: FullCustomerInfo) => void
}

export function CustomerSearchSelect({ onSelectCustomer }: CustomerSearchSelectProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<FullCustomerInfo | null>(null)

  // 过滤客户列表
  const filteredCustomers = mockCustomers.filter((customer: FullCustomerInfo) => {
    if (!searchTerm.trim()) return true
    
    const searchLower = searchTerm.toLowerCase()
    const customerName = getCustomerDisplayName(customer).toLowerCase()
    const customerPhone = getCustomerPhone(customer).toLowerCase()
    const customerEmail = getCustomerEmail(customer).toLowerCase()
    const customerIndex = customer.index?.toLowerCase() || ''
    
    return customerName.includes(searchLower) ||
           customerPhone.includes(searchLower) ||
           customerEmail.includes(searchLower) ||
           customerIndex.includes(searchLower)
  })

  // 获取客户显示名称
  const getCustomerDisplayName = (customer: FullCustomerInfo) => {
    switch (customer.customer_type) {
      case CustomerType.OWNER:
        return customer.owner_info?.owner_name || customer.name
      case CustomerType.MAINCONTRACTOR:
        return customer.maincontractor_info?.company_name || customer.name
      case CustomerType.BROKER:
        return customer.broker_info?.broker_name || customer.name
      default:
        return customer.name
    }
  }

  // 获取客户电话
  const getCustomerPhone = (customer: FullCustomerInfo) => {
    switch (customer.customer_type) {
      case CustomerType.OWNER:
        return customer.owner_info?.owner_phone || ''
      case CustomerType.MAINCONTRACTOR:
        return customer.maincontractor_info?.company_phone || ''
      case CustomerType.BROKER:
        return customer.broker_info?.broker_phone || ''
      default:
        return ''
    }
  }

  // 获取客户邮箱
  const getCustomerEmail = (customer: FullCustomerInfo) => {
    switch (customer.customer_type) {
      case CustomerType.OWNER:
        return customer.owner_info?.email || ''
      case CustomerType.MAINCONTRACTOR:
        return customer.maincontractor_info?.company_email || ''
      case CustomerType.BROKER:
        return customer.broker_info?.broker_email || ''
      default:
        return ''
    }
  }

  // 获取客户类型图标
  const getCustomerTypeIcon = (type: CustomerType) => {
    switch (type) {
      case CustomerType.OWNER:
        return <Building className="h-4 w-4" />
      case CustomerType.MAINCONTRACTOR:
        return <Users className="h-4 w-4" />
      case CustomerType.BROKER:
        return <User className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  // 获取客户类型文本
  const getCustomerTypeText = (type: CustomerType) => {
    switch (type) {
      case CustomerType.OWNER:
        return '业主'
      case CustomerType.MAINCONTRACTOR:
        return '总包'
      case CustomerType.BROKER:
        return '中间人'
      default:
        return '未知'
    }
  }

  // 选择客户
  const handleSelectCustomer = (customer: FullCustomerInfo) => {
    setSelectedCustomer(customer)
    onSelectCustomer(customer)
    setOpen(false)
    toast.success(`已导入客户：${getCustomerDisplayName(customer)}`)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="ml-auto">
          <Search className="h-4 w-4 mr-2" />
          导入已有客户
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>选择已有客户</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索客户名称、电话或邮箱..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>

          {/* 客户列表 */}
          <div className="max-h-96 overflow-y-auto border rounded-md">
            {filteredCustomers.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">
                {searchTerm ? '未找到匹配的客户' : '暂无客户数据'}
              </div>
            ) : (
              <div className="space-y-2 p-2">
                {filteredCustomers.map((customer: FullCustomerInfo) => (
                  <div
                    key={customer.id}
                    className="p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => handleSelectCustomer(customer)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          {getCustomerTypeIcon(customer.customer_type)}
                          <span className="font-medium">
                            {getCustomerDisplayName(customer)}
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            {getCustomerTypeText(customer.customer_type)}
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-muted-foreground space-y-1">
                          {getCustomerPhone(customer) && (
                            <div>电话: {getCustomerPhone(customer)}</div>
                          )}
                          {getCustomerEmail(customer) && (
                            <div>邮箱: {getCustomerEmail(customer)}</div>
                          )}
                          <div>编号: {customer.index}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 