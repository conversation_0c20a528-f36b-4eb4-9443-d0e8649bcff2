"use client"

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {Loader2} from "lucide-react"
import {FullCustomerInfo, CustomerType} from '@/api/customer/types'

// 获取客户名称的工具函数
const getCustomerName = (customer: FullCustomerInfo): string => {
  switch (customer.customer_type) {
    case CustomerType.OWNER:
      return customer.owner_info?.owner_name || '';
    case CustomerType.MAINCONTRACTOR:
      return customer.maincontractor_info?.company_name || '';
    case CustomerType.BROKER:
      return customer.broker_info?.broker_name || '';
    default:
      return customer.name || '';
  }
}

interface ConfirmDeleteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  customer: FullCustomerInfo | null
  onConfirm: () => void
  isDeleting: boolean
}

export function ConfirmDeleteDialog({
  open,
  onOpenChange,
  customer,
  onConfirm,
  isDeleting,
}: ConfirmDeleteDialogProps) {
  if (!customer) return null; // 如果没有客户信息，不渲染对话框

  const customerName = getCustomerName(customer);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除客户 "<strong>{customerName}</strong>" (序列: {customer.index}) 吗？此操作无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={isDeleting} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                删除中...
              </>
            ) : (
              '确认删除'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
} 