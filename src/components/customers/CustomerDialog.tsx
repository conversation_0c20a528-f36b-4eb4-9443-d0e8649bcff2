"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { CustomerForm } from './CustomerForm'
import { FullCustomerInfo } from '@/api/customer/types'

interface CustomerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer?: FullCustomerInfo;
  onSave: (customerData: any) => void;
  title: string;
  isSaving?: boolean;
}

export function CustomerDialog({
  open,
  onOpenChange,
  customer,
  onSave,
  title,
  isSaving = false,
}: CustomerDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        
        <CustomerForm 
          customer={customer}
          onSubmit={onSave}
          isLoading={isSaving}
        />
      </DialogContent>
    </Dialog>
  )
} 