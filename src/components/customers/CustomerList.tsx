"use client"

import {useCallback, useEffect, useState} from 'react'
import {FullCustomerInfo, CustomerOperation, CustomerType, ImportanceLevel} from '@/api/customer/types'
import {
    createCustomerRequest,
    deleteCustomerRequest,
    getPaginatedCustomerListRequest,
    updateCustomerRequest
} from '@/api/customer/api'
import {CustomerTable} from './CustomerTable'
import {CustomerSearch} from './CustomerSearch'
import {CustomerDialog} from './CustomerDialog'
import {ConfirmDeleteDialog} from './ConfirmDeleteDialog'
import {FilterState} from './CustomerFilter'
import {toast} from 'sonner'
import {Loader2} from 'lucide-react'
import {useDebounce} from '@/hooks/use-debounce'

// Mock数据
const mockCustomers: FullCustomerInfo[] = [
  {
    id: 1,
    name: '华为技术有限公司',
    customer_type: CustomerType.OWNER,
    index: 'OWN001',
    owner_info: {
      owner_name: '华为技术有限公司',
      owner_phone: '0755-28560000',
      contacts: [
        { name: '张总', phone: '13800138001' },
        { name: '李经理', phone: '13800138002' }
      ],
      address: '广东省深圳市龙岗区坂田华为基地',
      email: '<EMAIL>',
      registration_date: '2024-01-15',
      staff_member: '王业务员',
      importance_level: ImportanceLevel.A,
      remark: '重要客户，项目金额较大'
    }
  },
  {
    id: 2,
    name: '中建三局集团有限公司',
    customer_type: CustomerType.MAINCONTRACTOR,
    index: 'CON001',
    maincontractor_info: {
      company_name: '中建三局集团有限公司',
      company_phone: '027-82926688',
      contacts: [
        { name: '刘项目经理', phone: '13700137001' },
        { name: '陈工程师', phone: '13700137002' }
      ],
      company_address: '湖北省武汉市洪山区中建三局大厦',
      company_email: '<EMAIL>',
      registration_date: '2024-01-20',
      staff_member: '张业务员',
      importance_level: ImportanceLevel.A,
      remark: '长期合作伙伴，信誉良好'
    }
  },
  {
    id: 3,
    name: '腾讯科技',
    customer_type: CustomerType.OWNER,
    index: 'OWN002',
    owner_info: {
      owner_name: '腾讯科技(深圳)有限公司',
      owner_phone: '0755-86013388',
      contacts: [
        { name: '马总监', phone: '13600136001' }
      ],
      address: '广东省深圳市南山区腾讯大厦',
      email: '<EMAIL>',
      registration_date: '2024-02-01',
      staff_member: '李业务员',
      importance_level: ImportanceLevel.B,
      remark: '互联网头部企业'
    }
  },
  {
    id: 4,
    name: '王中介',
    customer_type: CustomerType.BROKER,
    index: 'BRK001',
    broker_info: {
      broker_name: '王中介',
      broker_phone: '13500135001',
      broker_email: '<EMAIL>',
      registration_date: '2024-02-10',
      staff_member: '赵业务员',
      importance_level: ImportanceLevel.B,
      remark: '资源丰富，配合度高'
    }
  },
  {
    id: 5,
    name: '中国建筑股份有限公司',
    customer_type: CustomerType.MAINCONTRACTOR,
    index: 'CON002',
    maincontractor_info: {
      company_name: '中国建筑股份有限公司',
      company_phone: '010-88082888',
      contacts: [
        { name: '周总工', phone: '13400134001' },
        { name: '吴经理', phone: '13400134002' },
        { name: '钱主管', phone: '13400134003' }
      ],
      company_address: '北京市朝阳区金台西路2号院1号楼',
      company_email: '<EMAIL>',
      registration_date: '2024-02-15',
      staff_member: '孙业务员',
      importance_level: ImportanceLevel.A,
      remark: '国内建筑行业龙头企业'
    }
  },
  {
    id: 6,
    name: '阿里巴巴集团',
    customer_type: CustomerType.OWNER,
    index: 'OWN003',
    owner_info: {
      owner_name: '阿里巴巴(中国)有限公司',
      owner_phone: '0571-85022088',
      contacts: [
        { name: '杨总', phone: '13300133001' }
      ],
      address: '浙江省杭州市余杭区文一西路969号',
      email: '<EMAIL>',
      registration_date: '2024-02-20',
      staff_member: '周业务员',
      importance_level: ImportanceLevel.A,
      remark: '电商巨头，多个项目在谈'
    }
  },
  {
    id: 7,
    name: '李经纪人',
    customer_type: CustomerType.BROKER,
    index: 'BRK002',
    broker_info: {
      broker_name: '李经纪人',
      broker_phone: '13200132001',
      broker_email: '<EMAIL>',
      registration_date: '2024-02-25',
      staff_member: '吴业务员',
      importance_level: ImportanceLevel.C,
      remark: '新接触的中介，待观察'
    }
  },
  {
    id: 8,
    name: '万科企业股份有限公司',
    customer_type: CustomerType.OWNER,
    index: 'OWN004',
    owner_info: {
      owner_name: '万科企业股份有限公司',
      owner_phone: '0755-25606666',
      contacts: [
        { name: '胡副总', phone: '13100131001' },
        { name: '林总监', phone: '13100131002' }
      ],
      address: '广东省深圳市盐田区大梅沙环梅路33号',
      email: '<EMAIL>',
      registration_date: '2024-03-01',
      staff_member: '郑业务员',
      importance_level: ImportanceLevel.A,
      remark: '房地产行业领军企业'
    }
  }
];

// 获取客户的重要程度
const getCustomerImportanceLevel = (customer: FullCustomerInfo): ImportanceLevel => {
  switch (customer.customer_type) {
    case CustomerType.OWNER:
      return customer.owner_info?.importance_level || ImportanceLevel.B;
    case CustomerType.MAINCONTRACTOR:
      return customer.maincontractor_info?.importance_level || ImportanceLevel.B;
    case CustomerType.BROKER:
      return customer.broker_info?.importance_level || ImportanceLevel.B;
    default:
      return ImportanceLevel.B;
  }
}

// 获取客户的注册日期
const getCustomerRegistrationDate = (customer: FullCustomerInfo): string => {
  switch (customer.customer_type) {
    case CustomerType.OWNER:
      return customer.owner_info?.registration_date || '';
    case CustomerType.MAINCONTRACTOR:
      return customer.maincontractor_info?.registration_date || '';
    case CustomerType.BROKER:
      return customer.broker_info?.registration_date || '';
    default:
      return '';
  }
}

export function CustomerList() {
  const [customers, setCustomers] = useState<FullCustomerInfo[]>([])
  const [isLoading, setIsLoading] = useState(true) // 初始加载
  const [isSaving, setIsSaving] = useState(false) // 保存操作加载
  const [isDeleting, setIsDeleting] = useState(false) // 删除操作加载状态
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  
  // 筛选状态
  const [filters, setFilters] = useState<FilterState>({
    customerType: 'all',
    importanceLevel: 'all',
    dateFrom: '',
    dateTo: ''
  })
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize] = useState(16)
  
  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogTitle, setDialogTitle] = useState('')
  const [currentCustomer, setCurrentCustomer] = useState<FullCustomerInfo | undefined>(undefined)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false) // 删除确认对话框状态
  const [customerToDelete, setCustomerToDelete] = useState<FullCustomerInfo | null>(null) // 要删除的客户信息
  
  // 筛选和搜索逻辑
  const filterCustomers = useCallback((customers: FullCustomerInfo[], search: string, filterState: FilterState) => {
    let filtered = customers;

    // 搜索过滤
    if (search.trim()) {
      filtered = filtered.filter(customer => {
        const searchLower = search.toLowerCase();
        // 根据客户类型搜索不同字段
        switch (customer.customer_type) {
          case CustomerType.OWNER:
            return customer.owner_info?.owner_name?.toLowerCase().includes(searchLower) ||
                   customer.owner_info?.owner_phone?.includes(search);
          case CustomerType.MAINCONTRACTOR:
            return customer.maincontractor_info?.company_name?.toLowerCase().includes(searchLower) ||
                   customer.maincontractor_info?.company_phone?.includes(search);
          case CustomerType.BROKER:
            return customer.broker_info?.broker_name?.toLowerCase().includes(searchLower) ||
                   customer.broker_info?.broker_phone?.includes(search);
          default:
            return false;
        }
      });
    }

    // 客户类型筛选
    if (filterState.customerType !== 'all') {
      filtered = filtered.filter(customer => customer.customer_type === filterState.customerType);
    }

    // 重要程度筛选
    if (filterState.importanceLevel !== 'all') {
      filtered = filtered.filter(customer => {
        const importance = getCustomerImportanceLevel(customer);
        return importance === filterState.importanceLevel;
      });
    }

    // 日期范围筛选
    if (filterState.dateFrom || filterState.dateTo) {
      filtered = filtered.filter(customer => {
        const registrationDate = getCustomerRegistrationDate(customer);
        if (!registrationDate) return false;

        const customerDate = new Date(registrationDate);
        const fromDate = filterState.dateFrom ? new Date(filterState.dateFrom) : null;
        const toDate = filterState.dateTo ? new Date(filterState.dateTo) : null;

        if (fromDate && customerDate < fromDate) return false;
        if (toDate && customerDate > toDate) return false;

        return true;
      });
    }

    return filtered;
  }, []);
  
  // 模拟获取分页客户列表
  const fetchPaginatedCustomers = useCallback(async (page: number, search: string, filterState: FilterState) => {
    setIsLoading(true)
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    try {
      // 应用筛选和搜索
      const filteredCustomers = filterCustomers(mockCustomers, search, filterState);
      
      // 分页处理
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);
      
      setCustomers(paginatedCustomers);
      setTotalPages(Math.max(1, Math.ceil(filteredCustomers.length / pageSize)));
      
    } catch (error) {
      console.error("获取客户列表错误:", error)
      toast.error("获取客户列表失败，请稍后重试")
      setCustomers([])
      setTotalPages(1)
    } finally {
      setIsLoading(false)
    }
  }, [pageSize, filterCustomers])
  
  // 初始化加载和相关状态变化时加载数据
  useEffect(() => {
    fetchPaginatedCustomers(currentPage, debouncedSearchTerm, filters)
  }, [currentPage, debouncedSearchTerm, filters, fetchPaginatedCustomers])
  
  // 处理搜索输入变化
  const handleSearchInputChange = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1) 
  }
  
  // 处理筛选变化
  const handleFiltersChange = (newFilters: FilterState) => {
    setFilters(newFilters)
    setCurrentPage(1) 
  }
  
  // 处理页面变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  // 打开新增客户对话框
  const handleAddClick = () => {
    setCurrentCustomer(undefined)
    setDialogTitle('新增客户')
    setDialogOpen(true)
  }
  
  // 打开编辑客户对话框
  const handleEditClick = (customer: FullCustomerInfo) => {
    setCurrentCustomer(customer)
    setDialogTitle('编辑客户')
    setDialogOpen(true)
  }
  
  // 修改 handleDeleteClick 以打开确认对话框
  const handleDeleteClick = (customer: FullCustomerInfo) => {
    setCustomerToDelete(customer)
    setIsDeleteDialogOpen(true)
  }
  
  // 处理确认删除
  const handleConfirmDelete = async () => {
    if (!customerToDelete?.index) return
    
    setIsDeleting(true)
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    try {
      // 模拟删除成功
      toast.success(`客户删除成功`)
      // 从本地状态中移除被删除的客户
      setCustomers(prevCustomers => 
        prevCustomers.filter(cust => cust.index !== customerToDelete.index)
      )
      setIsDeleteDialogOpen(false)
      setCustomerToDelete(null)
      
      // 重新获取数据以保持分页正确
      fetchPaginatedCustomers(currentPage, debouncedSearchTerm, filters)
      
    } catch (error) {
      console.error("删除客户错误:", error)
      toast.error("删除客户时发生错误，请稍后重试")
    } finally {
      setIsDeleting(false)
    }
  }
  
  // 处理保存客户
  const handleSaveCustomer = async (customerData: CustomerOperation & { index?: string }) => {
    setIsSaving(true)
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    try {
      if (customerData.index) {
        // 编辑现有客户
        toast.success(`客户更新成功`)
        setDialogOpen(false)
        // 刷新列表
        fetchPaginatedCustomers(currentPage, debouncedSearchTerm, filters)
      } else {
        // 新增客户
        // 根据客户类型获取名称用于显示
        let customerName = ''
        if (customerData.owner_info) {
          customerName = customerData.owner_info.owner_name || ''
        } else if (customerData.maincontractor_info) {
          customerName = customerData.maincontractor_info.company_name || ''
        } else if (customerData.broker_info) {
          customerName = customerData.broker_info.broker_name || ''
        }
        
        toast.success(`客户 "${customerName}" 创建成功`)
        setDialogOpen(false)
        // 刷新列表到第一页
        if (currentPage === 1) {
          fetchPaginatedCustomers(1, debouncedSearchTerm, filters)
        } else {
          setCurrentPage(1)
        }
      }
    } catch (error) {
      console.error("保存客户错误:", error)
      toast.error("保存客户时发生错误，请稍后重试")
    } finally {
      setIsSaving(false)
    }
  }

  // 显示全页面加载状态
  // 注意：只在没有搜索词且首次加载时显示全屏加载
  // 如果是带搜索词的加载或者翻页加载，使用表格内的加载状态
  if (isLoading && customers.length === 0 && !debouncedSearchTerm) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">正在加载客户数据...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <CustomerSearch 
        onSearch={handleSearchInputChange} 
        onAddClick={handleAddClick}
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />
      
      <CustomerTable 
        customers={customers} 
        isLoading={isLoading && customers.length === 0} 
        onEdit={handleEditClick}
        onDelete={handleDeleteClick}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
      
      <CustomerDialog 
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        customer={currentCustomer}
        onSave={handleSaveCustomer}
        title={dialogTitle}
        isSaving={isSaving} // 传递 isSaving 状态
      />

      <ConfirmDeleteDialog 
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        customer={customerToDelete}
        onConfirm={handleConfirmDelete}
        isDeleting={isDeleting}
      />
    </>
  )
} 