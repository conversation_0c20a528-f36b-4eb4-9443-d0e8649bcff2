"use client"

import {useState} from 'react'
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from '@/components/ui/table'
import {Button} from '@/components/ui/button'
import {Badge} from '@/components/ui/badge'
import {Check, Copy, Edit, Trash2} from 'lucide-react'
import {Skeleton} from '@/components/ui/skeleton'
import {useIsMobile} from '@/hooks/use-mobile'
import {toast} from 'sonner'
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger,} from '@/components/ui/tooltip'

import {PaginationControls} from '@/components/share/pagination'
import {FullCustomerInfo, CustomerType, ImportanceLevel} from '@/api/customer/types'

// 截断字符串的工具函数
const truncateString = (str: string, maxLength: number) => {
    if (!str) return '';
    return str.length > maxLength ? str.substring(0, maxLength) + '...' : str;
}

// 复制到剪贴板的工具函数
const copyToClipboard = async (text: string, description: string) => {
    try {
        await navigator.clipboard.writeText(text);
        toast.success(`${description}已复制到剪贴板`);
    } catch (error) {
        console.error('复制失败:', error);
        toast.error('复制失败，请手动复制');
    }
}

// 获取客户类型显示文本和颜色
const getCustomerTypeInfo = (type: CustomerType) => {
  switch (type) {
    case CustomerType.OWNER:
      return { text: '业主', color: 'bg-blue-100 text-blue-800' }
    case CustomerType.MAINCONTRACTOR:
      return { text: '总包', color: 'bg-green-100 text-green-800' }
    case CustomerType.BROKER:
      return { text: '中间人', color: 'bg-purple-100 text-purple-800' }
    default:
      return { text: '未知', color: 'bg-gray-100 text-gray-800' }
  }
}

// 获取重要程度显示颜色
const getImportanceLevelColor = (level: ImportanceLevel) => {
  switch (level) {
    case ImportanceLevel.A:
      return 'bg-red-100 text-red-800'
    case ImportanceLevel.B:
      return 'bg-yellow-100 text-yellow-800'
    case ImportanceLevel.C:
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取客户主要信息
const getCustomerMainInfo = (customer: FullCustomerInfo) => {
  switch (customer.customer_type) {
    case CustomerType.OWNER:
      return {
        name: customer.owner_info?.owner_name || '',
        phone: customer.owner_info?.owner_phone || '',
        address: customer.owner_info?.address || '',
        email: customer.owner_info?.email || '',
        importanceLevel: customer.owner_info?.importance_level || ImportanceLevel.B,
        remark: customer.owner_info?.remark || '',
        registrationDate: customer.owner_info?.registration_date || '',
        staffMember: customer.owner_info?.staff_member || '',
        contacts: customer.owner_info?.contacts || []
      }
    case CustomerType.MAINCONTRACTOR:
      return {
        name: customer.maincontractor_info?.company_name || '',
        phone: customer.maincontractor_info?.company_phone || '',
        address: customer.maincontractor_info?.company_address || '',
        email: customer.maincontractor_info?.company_email || '',
        importanceLevel: customer.maincontractor_info?.importance_level || ImportanceLevel.B,
        remark: customer.maincontractor_info?.remark || '',
        registrationDate: customer.maincontractor_info?.registration_date || '',
        staffMember: customer.maincontractor_info?.staff_member || '',
        contacts: customer.maincontractor_info?.contacts || []
      }
    case CustomerType.BROKER:
      return {
        name: customer.broker_info?.broker_name || '',
        phone: customer.broker_info?.broker_phone || '',
        address: '',
        email: customer.broker_info?.broker_email || '',
        importanceLevel: customer.broker_info?.importance_level || ImportanceLevel.B,
        remark: customer.broker_info?.remark || '',
        registrationDate: customer.broker_info?.registration_date || '',
        staffMember: customer.broker_info?.staff_member || '',
        contacts: []
      }
    default:
      return {
        name: '',
        phone: '',
        address: '',
        email: '',
        importanceLevel: ImportanceLevel.B,
        remark: '',
        registrationDate: '',
        staffMember: '',
        contacts: []
      }
  }
}

interface CustomerTableProps {
    customers: FullCustomerInfo[];
    isLoading: boolean;
    onEdit: (customer: FullCustomerInfo) => void;
    onDelete: (customer: FullCustomerInfo) => void;
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
}

export function CustomerTable({
                                  customers,
                                  isLoading,
                                  onEdit,
                                  onDelete,
                                  currentPage,
                                  totalPages,
                                  onPageChange,
                              }: CustomerTableProps) {
    const isMobile = useIsMobile();

    if (isLoading) {
        return (
            <div>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[10%]">序列</TableHead>
                            <TableHead className="w-[15%]">客户类型</TableHead>
                            <TableHead className="w-[20%]">名称</TableHead>
                            {!isMobile && (
                                <>
                                    <TableHead className="w-[15%]">电话</TableHead>
                                    <TableHead className="w-[15%]">重要程度</TableHead>
                                    <TableHead className="w-[10%]">登录日期</TableHead>
                                </>
                            )}
                            <TableHead className="w-[15%]">操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {Array.from({length: 8}).map((_, index) => (
                            <TableRow key={index}>
                                <TableCell><Skeleton className="h-4 w-8"/></TableCell>
                                <TableCell><Skeleton className="h-4 w-16"/></TableCell>
                                <TableCell><Skeleton className="h-4 w-24"/></TableCell>
                                {!isMobile && (
                                    <>
                                        <TableCell><Skeleton className="h-4 w-20"/></TableCell>
                                        <TableCell><Skeleton className="h-4 w-12"/></TableCell>
                                        <TableCell><Skeleton className="h-4 w-20"/></TableCell>
                                    </>
                                )}
                                <TableCell>
                                    <div className="flex space-x-2">
                                        <Skeleton className="h-8 w-8"/>
                                        <Skeleton className="h-8 w-8"/>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        )
    }

    return (
        <TooltipProvider>
            <div className="overflow-x-auto">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[10%]">序列</TableHead>
                            <TableHead className="w-[15%]">客户类型</TableHead>
                            <TableHead className="w-[20%]">名称</TableHead>
                            {!isMobile && (
                                <>
                                    <TableHead className="w-[15%]">电话</TableHead>
                                    <TableHead className="w-[15%]">重要程度</TableHead>
                                    <TableHead className="w-[10%]">登录日期</TableHead>
                                </>
                            )}
                            <TableHead className="w-[15%]">操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {customers.length === 0 ? (
                            <TableRow>
                                <TableCell 
                                    colSpan={isMobile ? 4 : 7} 
                                    className="text-center py-8 text-muted-foreground"
                                >
                                    暂无客户数据
                                </TableCell>
                            </TableRow>
                        ) : (
                            customers.map((customer, index) => {
                                const mainInfo = getCustomerMainInfo(customer);
                                const typeInfo = getCustomerTypeInfo(customer.customer_type);
                                
                                return (
                                    <TableRow key={customer.index || index}>
                                        <TableCell className="font-medium">
                                            {customer.index || (currentPage - 1) * 16 + index + 1}
                                        </TableCell>
                                        
                                        <TableCell>
                                            <Badge className={typeInfo.color}>
                                                {typeInfo.text}
                                            </Badge>
                                        </TableCell>
                                        
                                        <TableCell>
                                            <div className="flex items-center space-x-2">
                                                <span className="font-medium">
                                                    {truncateString(mainInfo.name, 20)}
                                                </span>
                                                {mainInfo.name && (
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => copyToClipboard(mainInfo.name, '客户名称')}
                                                                className="h-6 w-6 p-0"
                                                            >
                                                                <Copy className="h-3 w-3"/>
                                                            </Button>
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>复制客户名称</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                )}
                                            </div>
                                        </TableCell>

                                        {!isMobile && (
                                            <>
                                                <TableCell>
                                                    <div className="flex items-center space-x-2">
                                                        <span>{truncateString(mainInfo.phone, 15)}</span>
                                                        {mainInfo.phone && (
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() => copyToClipboard(mainInfo.phone, '电话号码')}
                                                                        className="h-6 w-6 p-0"
                                                                    >
                                                                        <Copy className="h-3 w-3"/>
                                                                    </Button>
                                                                </TooltipTrigger>
                                                                <TooltipContent>
                                                                    <p>复制电话号码</p>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                
                                                <TableCell>
                                                    <Badge className={getImportanceLevelColor(mainInfo.importanceLevel)}>
                                                        {mainInfo.importanceLevel}级
                                                    </Badge>
                                                </TableCell>
                                                
                                                <TableCell>
                                                    <span className="text-sm text-muted-foreground">
                                                        {mainInfo.registrationDate}
                                                    </span>
                                                </TableCell>
                                            </>
                                        )}

                                        <TableCell>
                                            <div className="flex items-center space-x-1">
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => onEdit(customer)}
                                                            className="h-8 w-8 p-0"
                                                        >
                                                            <Edit className="h-4 w-4"/>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>编辑客户</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => onDelete(customer)}
                                                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                                        >
                                                            <Trash2 className="h-4 w-4"/>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>删除客户</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                )
                            })
                        )}
                    </TableBody>
                </Table>

                {/* 分页控件 */}
                {totalPages > 1 && (
                    <div className="mt-4">
                        <PaginationControls
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={onPageChange}
                            isLoading={isLoading}
                        />
                    </div>
                )}
            </div>
        </TooltipProvider>
    )
} 