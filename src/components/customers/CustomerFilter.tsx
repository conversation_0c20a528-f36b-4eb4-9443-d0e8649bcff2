"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { CalendarIcon, Filter, X } from 'lucide-react'
import { CustomerType, ImportanceLevel } from '@/api/customer/types'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export interface FilterState {
  customerType: CustomerType | 'all'
  importanceLevel: ImportanceLevel | 'all'
  dateFrom: string
  dateTo: string
}

interface CustomerFilterProps {
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
}

const customerTypeOptions = [
  { value: 'all', label: '全部类型' },
  { value: CustomerType.OWNER, label: '业主' },
  { value: CustomerType.MAINCONTRACTOR, label: '总包' },
  { value: CustomerType.BROKER, label: '中间人' },
]

const importanceLevelOptions = [
  { value: 'all', label: '全部级别' },
  { value: ImportanceLevel.A, label: 'A级' },
  { value: ImportanceLevel.B, label: 'B级' },
  { value: ImportanceLevel.C, label: 'C级' },
]

export function CustomerFilter({ filters, onFiltersChange }: CustomerFilterProps) {
  const [isOpen, setIsOpen] = useState(false)

  const updateFilter = (key: keyof FilterState, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      customerType: 'all',
      importanceLevel: 'all',
      dateFrom: '',
      dateTo: ''
    })
  }

  const hasActiveFilters = filters.customerType !== 'all' || 
                          filters.importanceLevel !== 'all' || 
                          filters.dateFrom || 
                          filters.dateTo

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.customerType !== 'all') count++
    if (filters.importanceLevel !== 'all') count++
    if (filters.dateFrom || filters.dateTo) count++
    return count
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="relative">
          <Filter className="h-4 w-4 mr-2" />
          筛选
          {hasActiveFilters && (
            <Badge 
              variant="destructive" 
              className="ml-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {getActiveFilterCount()}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">筛选条件</CardTitle>
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="h-7 text-xs"
                >
                  清除全部
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 客户类型筛选 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">客户类型</Label>
              <Select 
                value={filters.customerType} 
                onValueChange={(value) => updateFilter('customerType', value)}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="选择客户类型" />
                </SelectTrigger>
                <SelectContent>
                  {customerTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 重要程度筛选 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">重要程度</Label>
              <Select 
                value={filters.importanceLevel} 
                onValueChange={(value) => updateFilter('importanceLevel', value)}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="选择重要程度" />
                </SelectTrigger>
                <SelectContent>
                  {importanceLevelOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 注册日期筛选 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">注册日期范围</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs text-gray-500">开始日期</Label>
                  <Input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => updateFilter('dateFrom', e.target.value)}
                    className="h-9 pr-3"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-500">结束日期</Label>
                  <Input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => updateFilter('dateTo', e.target.value)}
                    className="h-9 pr-3"
                  />
                </div>
              </div>
            </div>

            {/* 当前激活的筛选条件 */}
            {hasActiveFilters && (
              <div className="pt-2 border-t">
                <Label className="text-sm font-medium mb-2 block">当前筛选</Label>
                <div className="flex flex-wrap gap-1">
                  {filters.customerType !== 'all' && (
                    <Badge variant="secondary" className="text-xs">
                      {customerTypeOptions.find(opt => opt.value === filters.customerType)?.label}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                        onClick={() => updateFilter('customerType', 'all')}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  )}
                  {filters.importanceLevel !== 'all' && (
                    <Badge variant="secondary" className="text-xs">
                      {importanceLevelOptions.find(opt => opt.value === filters.importanceLevel)?.label}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                        onClick={() => updateFilter('importanceLevel', 'all')}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  )}
                  {(filters.dateFrom || filters.dateTo) && (
                    <Badge variant="secondary" className="text-xs">
                      {filters.dateFrom || '开始'} ~ {filters.dateTo || '结束'}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                        onClick={() => {
                          updateFilter('dateFrom', '')
                          updateFilter('dateTo', '')
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
} 