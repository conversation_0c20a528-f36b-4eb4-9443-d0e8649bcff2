// next.config.ts
import type {NextConfig} from "next";

const nextConfig: NextConfig = {
    // 取消eslint 检查
    eslint: {
        ignoreDuringBuilds: true,
    },
    // 取消路径别名检查
    typescript: {
        ignoreBuildErrors: true,
    },
    reactStrictMode: false,
    // 隐藏所有开发者标记
    devIndicators: {
        buildActivity: false,       // 隐藏构建活动指示器
        buildActivityPosition: 'bottom-right',  // 设置位置（虽然已禁用）
    },
    // 禁用开发模式下的性能指标
    experimental: {
        optimizePackageImports: ['@radix-ui/react-icons'],
    },
    // 生产环境配置
    poweredByHeader: false,        // 隐藏 X-Powered-By 响应头
    compress: true,                // 启用 gzip 压缩
};

export default nextConfig;